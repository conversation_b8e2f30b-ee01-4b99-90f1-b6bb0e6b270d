{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/app/%28features%29/workflows/api.ts"], "sourcesContent": ["/**\r\n * Workflow API Module\r\n *\r\n * This module provides functions for interacting with the workflow API endpoints.\r\n */\r\n\r\nimport { workflowApi, externalApi } from \"@/utils/axios\";\r\nimport { getAccessToken } from \"@/lib/cookies\";\r\nimport { getClientAccessToken } from \"@/lib/clientCookies\";\r\nimport { API_ENDPOINTS } from \"@/lib/apiConfig\";\r\n\r\n// Types for workflow API responses\r\nexport interface WorkflowMetadata {\r\n  total: number;\r\n  totalPages: number;\r\n  currentPage: number;\r\n  pageSize: number;\r\n  hasNextPage: boolean;\r\n  hasPreviousPage: boolean;\r\n}\r\n\r\nexport interface WorkflowDetails {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  workflow_url?: string;\r\n  builder_url?: string;\r\n  start_nodes?: string[];\r\n  owner_id?: string;\r\n  user_id?: string; // Some APIs use user_id instead of owner_id\r\n  user_ids?: string[];\r\n  owner_type?: string;\r\n  workflow_template_id?: string;\r\n  parent_workflow_id?: string;\r\n  url?: string;\r\n  is_imported?: boolean;\r\n  execution_count?: number;\r\n  version?: string;\r\n  visibility?: string;\r\n  category?: string;\r\n  tags?: Record<string, any> | string[];\r\n  status?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface WorkflowSummary {\r\n  success?: boolean;\r\n  message?: string;\r\n  workflow: WorkflowDetails;\r\n}\r\n\r\nexport interface WorkflowListItem {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  workflow_url?: string;\r\n  builder_url?: string;\r\n  start_nodes?: string[];\r\n  owner_id?: string;\r\n  user_ids?: string[];\r\n  owner_type?: string;\r\n  workflow_template_id?: string;\r\n  parent_workflow_id?: string;\r\n  url?: string;\r\n  is_imported?: boolean;\r\n  execution_count?: number;\r\n  version?: string;\r\n  visibility?: string;\r\n  category?: string;\r\n  tags?: Record<string, any>;\r\n  status?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface WorkflowListResponse {\r\n  data: WorkflowListItem[];\r\n  metadata: WorkflowMetadata;\r\n}\r\n\r\nexport interface CreateWorkflowRequest {\r\n  name: string;\r\n  description: string;\r\n  workflow_data: {\r\n    nodes: Record<string, any>[];\r\n    edges: Record<string, any>[];\r\n  };\r\n  start_node_data?: string[];\r\n}\r\n\r\nexport interface CreateWorkflowResponse {\r\n  success: boolean;\r\n  message: string;\r\n  workflow_id: WorkflowDetails;\r\n}\r\n\r\n// Note: Using centralized workflowApi from @/utils/axios\r\n\r\n/**\r\n * Fetches workflows for the current user\r\n *\r\n * This function uses the /user/me endpoint which automatically identifies the user\r\n * from their authentication token, eliminating the need to pass a user ID.\r\n */\r\nexport async function fetchWorkflowsByUser(\r\n  page: number = 1,\r\n  pageSize: number = 10,\r\n  accessToken?: string,\r\n): Promise<WorkflowListResponse> {\r\n  try {\r\n    console.log(`[DEBUG] Fetching workflows with page=${page}, pageSize=${pageSize}`);\r\n    console.log(`[DEBUG] Using endpoint: ${API_ENDPOINTS.WORKFLOWS.LIST}`);\r\n\r\n    // If no token is provided, try to get it from client or server\r\n    if (!accessToken) {\r\n      try {\r\n        if (typeof window !== \"undefined\") {\r\n          // Client-side\r\n          accessToken = getClientAccessToken();\r\n          console.log(\r\n            `[DEBUG] Retrieved client token (length: ${accessToken ? accessToken.length : 0})`,\r\n          );\r\n        } else {\r\n          // Server-side\r\n          accessToken = await getAccessToken();\r\n          console.log(\r\n            `[DEBUG] Retrieved server token (length: ${accessToken ? accessToken.length : 0})`,\r\n          );\r\n        }\r\n      } catch (tokenError) {\r\n        console.error(`[DEBUG] Error retrieving token:`, tokenError);\r\n      }\r\n    } else {\r\n      console.log(`[DEBUG] Using provided access token (length: ${accessToken.length})`);\r\n    }\r\n\r\n    // Prepare config with authentication if token is provided\r\n    const config: any = {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      withCredentials: false,\r\n      params: {\r\n        page: page,\r\n        page_size: pageSize,\r\n      },\r\n    };\r\n\r\n    // Add Authorization header if access token is provided\r\n    if (accessToken) {\r\n      // Ensure token has Bearer prefix\r\n      if (accessToken.startsWith(\"Bearer \")) {\r\n        config.headers.Authorization = accessToken;\r\n      } else {\r\n        config.headers.Authorization = `Bearer ${accessToken}`;\r\n      }\r\n      console.log(`[DEBUG] Added Authorization header`);\r\n    } else {\r\n      console.warn(`[DEBUG] No access token available for request`);\r\n    }\r\n\r\n    // Use the centralized workflowApi instance that has the interceptors\r\n    // and the correct endpoint from API_ENDPOINTS\r\n    console.log(`[DEBUG] Making request to: ${API_ENDPOINTS.WORKFLOWS.LIST}`);\r\n    const response = await workflowApi.get(API_ENDPOINTS.WORKFLOWS.LIST, config);\r\n    console.log(`[DEBUG] Successful response with ${response.data?.data?.length || 0} workflows`);\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error fetching workflows:\", error);\r\n    // Add more detailed error logging\r\n    if (error.response) {\r\n      console.error(`[DEBUG] Response status: ${error.response.status}`);\r\n      console.error(`[DEBUG] Response data:`, error.response.data);\r\n      throw new Error(\r\n        `Failed to fetch workflows: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    } else if (error.request) {\r\n      // The request was made but no response was received\r\n      console.error(`[DEBUG] No response received:`, error.request);\r\n      throw new Error(\"Failed to fetch workflows: No response received from server\");\r\n    } else {\r\n      // Something happened in setting up the request\r\n      console.error(`[DEBUG] Request setup error:`, error.message);\r\n      throw new Error(`Failed to fetch workflows: ${error.message}`);\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Creates a new workflow with a StartNode already added\r\n */\r\nexport async function createEmptyWorkflow(): Promise<CreateWorkflowResponse> {\r\n  try {\r\n    // Create a StartNode for the initial workflow\r\n    const startNode = {\r\n      id: \"start-node\",\r\n      type: \"WorkflowNode\",\r\n      position: { x: 100, y: 100 },\r\n      data: {\r\n        label: \"Start\",\r\n        type: \"component\",\r\n        originalType: \"StartNode\",\r\n        definition: {\r\n          name: \"StartNode\",\r\n          display_name: \"Start\",\r\n          description:\r\n            \"The starting point for all workflows. Only nodes connected to this node will be executed.\",\r\n          category: \"Input/Output\",\r\n          icon: \"Play\",\r\n          beta: false,\r\n          inputs: [],\r\n          outputs: [\r\n            {\r\n              name: \"flow\",\r\n              display_name: \"Flow\",\r\n              output_type: \"Any\",\r\n            },\r\n          ],\r\n          is_valid: true,\r\n          path: \"components.io.start_node\",\r\n        },\r\n        config: {\r\n          collected_parameters: {},\r\n        },\r\n      },\r\n    };\r\n\r\n    const payload: CreateWorkflowRequest = {\r\n      name: \"Untitled Workflow\",\r\n      description: \"New workflow\",\r\n      workflow_data: {\r\n        nodes: [startNode],\r\n        edges: [],\r\n      },\r\n      start_node_data: [],\r\n    };\r\n\r\n    // Get token for authorization\r\n    let token;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      token = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      token = await getAccessToken();\r\n    }\r\n\r\n    const config = {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        ...(token && { Authorization: `Bearer ${token}` }),\r\n      },\r\n      withCredentials: false,\r\n    };\r\n\r\n    const response = await workflowApi.post(API_ENDPOINTS.WORKFLOWS.CREATE, payload, config);\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error creating workflow:\", error);\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to create workflow: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches a specific workflow by ID\r\n *\r\n * @returns A WorkflowSummary object with the workflow details nested in the workflow property\r\n */\r\nexport async function fetchWorkflowById(id: string): Promise<WorkflowSummary> {\r\n  try {\r\n    // Get token for authorization\r\n    let token;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      token = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      token = await getAccessToken();\r\n    }\r\n\r\n    const config = {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        ...(token && { Authorization: `Bearer ${token}` }),\r\n      },\r\n      withCredentials: false,\r\n    };\r\n\r\n    const response = await workflowApi.get(API_ENDPOINTS.WORKFLOWS.GET(id), config);\r\n    console.log(\"Workflow API response:\", response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error fetching workflow:\", error);\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to fetch workflow: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches workflow data from a builder URL\r\n * @param url The URL to fetch the workflow data from\r\n * @returns The workflow data\r\n */\r\nexport async function fetchWorkflowFromBuilderUrl(url: string): Promise<any> {\r\n  try {\r\n    // For external URLs, we need to use the external API instance (no auth)\r\n    const response = await externalApi.get(url, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error fetching workflow from builder URL:\", error);\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to fetch workflow data: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Deletes a workflow by ID\r\n *\r\n * @param id The ID of the workflow to delete\r\n * @returns A response object indicating success or failure\r\n */\r\nexport async function deleteWorkflow(id: string): Promise<{ success: boolean; message: string }> {\r\n  try {\r\n    // Get token for authorization\r\n    let token;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      token = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      token = await getAccessToken();\r\n    }\r\n\r\n    const config = {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        ...(token && { Authorization: `Bearer ${token}` }),\r\n      },\r\n      withCredentials: false,\r\n    };\r\n\r\n    const response = await workflowApi.delete(API_ENDPOINTS.WORKFLOWS.DELETE(id), config);\r\n    console.log(\"Workflow delete response:\", response.data);\r\n    return {\r\n      success: true,\r\n      message: response.data?.message || \"Workflow deleted successfully\",\r\n    };\r\n  } catch (error: any) {\r\n    console.error(\"Error deleting workflow:\", error);\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to delete workflow: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Updates a workflow's metadata (name and description)\r\n */\r\nexport async function updateWorkflowMetadata(\r\n  id: string,\r\n  data: { name?: string; description?: string },\r\n): Promise<WorkflowSummary> {\r\n  try {\r\n    // Get token for authorization\r\n    let token;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      token = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      token = await getAccessToken();\r\n    }\r\n\r\n    const config = {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        ...(token && { Authorization: `Bearer ${token}` }),\r\n      },\r\n      withCredentials: false,\r\n    };\r\n\r\n    const response = await workflowApi.patch(API_ENDPOINTS.WORKFLOWS.UPDATE(id), data, config);\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error updating workflow metadata:\", error);\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to update workflow: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\nconst workflowApiExports = {\r\n  fetchWorkflowsByUser,\r\n  createEmptyWorkflow,\r\n  fetchWorkflowById,\r\n  fetchWorkflowFromBuilderUrl,\r\n  deleteWorkflow,\r\n  updateWorkflowMetadata,\r\n};\r\n\r\nexport default workflowApiExports;\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;AAED;AACA;AACA;AACA;;;;;AAgGO,eAAe,qBACpB,OAAe,CAAC,EAChB,WAAmB,EAAE,EACrB,WAAoB;IAEpB,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,KAAK,WAAW,EAAE,UAAU;QAChF,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,EAAE;QAErE,+DAA+D;QAC/D,IAAI,CAAC,aAAa;YAChB,IAAI;gBACF,uCAAmC;;gBAMnC,OAAO;oBACL,cAAc;oBACd,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;oBACjC,QAAQ,GAAG,CACT,CAAC,wCAAwC,EAAE,cAAc,YAAY,MAAM,GAAG,EAAE,CAAC,CAAC;gBAEtF;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAE;YACnD;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,YAAY,MAAM,CAAC,CAAC,CAAC;QACnF;QAEA,0DAA0D;QAC1D,MAAM,SAAc;YAClB,SAAS;gBACP,gBAAgB;YAClB;YACA,iBAAiB;YACjB,QAAQ;gBACN,MAAM;gBACN,WAAW;YACb;QACF;QAEA,uDAAuD;QACvD,IAAI,aAAa;YACf,iCAAiC;YACjC,IAAI,YAAY,UAAU,CAAC,YAAY;gBACrC,OAAO,OAAO,CAAC,aAAa,GAAG;YACjC,OAAO;gBACL,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa;YACxD;YACA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC;QAClD,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,6CAA6C,CAAC;QAC9D;QAEA,qEAAqE;QACrE,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,EAAE;QACxE,MAAM,WAAW,MAAM,qHAAA,CAAA,cAAW,CAAC,GAAG,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,EAAE;QACrE,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS,IAAI,EAAE,MAAM,UAAU,EAAE,UAAU,CAAC;QAE5F,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,kCAAkC;QAClC,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,MAAM,QAAQ,CAAC,MAAM,EAAE;YACjE,QAAQ,KAAK,CAAC,CAAC,sBAAsB,CAAC,EAAE,MAAM,QAAQ,CAAC,IAAI;YAC3D,MAAM,IAAI,MACR,CAAC,2BAA2B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAEtF,OAAO,IAAI,MAAM,OAAO,EAAE;YACxB,oDAAoD;YACpD,QAAQ,KAAK,CAAC,CAAC,6BAA6B,CAAC,EAAE,MAAM,OAAO;YAC5D,MAAM,IAAI,MAAM;QAClB,OAAO;YACL,+CAA+C;YAC/C,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE,MAAM,OAAO;YAC3D,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,MAAM,OAAO,EAAE;QAC/D;IACF;AACF;AAKO,eAAe;IACpB,IAAI;QACF,8CAA8C;QAC9C,MAAM,YAAY;YAChB,IAAI;YACJ,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAI;YAC3B,MAAM;gBACJ,OAAO;gBACP,MAAM;gBACN,cAAc;gBACd,YAAY;oBACV,MAAM;oBACN,cAAc;oBACd,aACE;oBACF,UAAU;oBACV,MAAM;oBACN,MAAM;oBACN,QAAQ,EAAE;oBACV,SAAS;wBACP;4BACE,MAAM;4BACN,cAAc;4BACd,aAAa;wBACf;qBACD;oBACD,UAAU;oBACV,MAAM;gBACR;gBACA,QAAQ;oBACN,sBAAsB,CAAC;gBACzB;YACF;QACF;QAEA,MAAM,UAAiC;YACrC,MAAM;YACN,aAAa;YACb,eAAe;gBACb,OAAO;oBAAC;iBAAU;gBAClB,OAAO,EAAE;YACX;YACA,iBAAiB,EAAE;QACrB;QAEA,8BAA8B;QAC9B,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAC7B;QAEA,MAAM,SAAS;YACb,SAAS;gBACP,gBAAgB;gBAChB,GAAI,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC,CAAC;YACnD;YACA,iBAAiB;QACnB;QAEA,MAAM,WAAW,MAAM,qHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS;QAEjF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,2BAA2B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAEtF;QACA,MAAM;IACR;AACF;AAOO,eAAe,kBAAkB,EAAU;IAChD,IAAI;QACF,8BAA8B;QAC9B,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAC7B;QAEA,MAAM,SAAS;YACb,SAAS;gBACP,gBAAgB;gBAChB,GAAI,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC,CAAC;YACnD;YACA,iBAAiB;QACnB;QAEA,MAAM,WAAW,MAAM,qHAAA,CAAA,cAAW,CAAC,GAAG,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK;QACxE,QAAQ,GAAG,CAAC,0BAA0B,SAAS,IAAI;QACnD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAErF;QACA,MAAM;IACR;AACF;AAOO,eAAe,4BAA4B,GAAW;IAC3D,IAAI;QACF,wEAAwE;QACxE,MAAM,WAAW,MAAM,qHAAA,CAAA,cAAW,CAAC,GAAG,CAAC,KAAK;YAC1C,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAE1F;QACA,MAAM;IACR;AACF;AAQO,eAAe,eAAe,EAAU;IAC7C,IAAI;QACF,8BAA8B;QAC9B,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAC7B;QAEA,MAAM,SAAS;YACb,SAAS;gBACP,gBAAgB;gBAChB,GAAI,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC,CAAC;YACnD;YACA,iBAAiB;QACnB;QAEA,MAAM,WAAW,MAAM,qHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK;QAC9E,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;QACtD,OAAO;YACL,SAAS;YACT,SAAS,SAAS,IAAI,EAAE,WAAW;QACrC;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,2BAA2B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAEtF;QACA,MAAM;IACR;AACF;AAKO,eAAe,uBACpB,EAAU,EACV,IAA6C;IAE7C,IAAI;QACF,8BAA8B;QAC9B,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAC7B;QAEA,MAAM,SAAS;YACb,SAAS;gBACP,gBAAgB;gBAChB,GAAI,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC,CAAC;YACnD;YACA,iBAAiB;QACnB;QAEA,MAAM,WAAW,MAAM,qHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,MAAM;QAEnF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,qCAAqC;QACnD,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,2BAA2B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAEtF;QACA,MAAM;IACR;AACF;AAEA,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Creates a debounced function that delays invoking func until after wait milliseconds\r\n * have elapsed since the last time the debounced function was invoked.\r\n *\r\n * @param func The function to debounce\r\n * @param wait The number of milliseconds to delay\r\n * @returns A debounced function\r\n */\r\nexport function debounce<T extends (...args: any[]) => any>(\r\n  func: T,\r\n  wait: number\r\n): (...args: Parameters<T>) => void {\r\n  let timeout: NodeJS.Timeout | null = null;\r\n\r\n  return function(...args: Parameters<T>) {\r\n    const later = () => {\r\n      timeout = null;\r\n      func(...args);\r\n    };\r\n\r\n    if (timeout !== null) {\r\n      clearTimeout(timeout);\r\n    }\r\n\r\n    timeout = setTimeout(later, wait);\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,SAAS,GAAG,IAAmB;QACpC,MAAM,QAAQ;YACZ,UAAU;YACV,QAAQ;QACV;QAEA,IAAI,YAAY,MAAM;YACpB,aAAa;QACf;QAEA,UAAU,WAAW,OAAO;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90 shadow-xs\",\r\n        destructive:\r\n          \"bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white shadow-xs\",\r\n        outline:\r\n          \"bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs\",\r\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-xs\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root;\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"focus:bg-accent data-[state=open]:bg-accent flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-none select-none\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n));\r\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName;\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-lg\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName;\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n));\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm transition-colors outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center rounded-sm py-1.5 pr-2 pl-8 text-sm transition-colors outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n));\r\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName;\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center rounded-sm py-1.5 pr-2 pl-8 text-sm transition-colors outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n));\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"bg-muted -mx-1 my-1 h-px\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\r\n\r\nconst DropdownMenuShortcut = ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)} {...props} />\r\n  );\r\n};\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAEA;;;;;;AAEA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAAG,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAAG,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC,SAAS,QAAQ;QACnE,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAAE,SAAS,EAAE,GAAG,OAA8C;IAC1F,qBACE,8OAAC;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAAa,GAAG,KAAK;;;;;;AAE3F;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction Select({ ...props }: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\r\n}\r\n\r\nfunction SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\r\n}\r\n\r\nfunction SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\";\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className,\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\",\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { buttonVariants } from \"@/components/ui/button\";\r\nimport { VariantProps } from \"class-variance-authority\";\r\n\r\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\r\n  <nav\r\n    role=\"navigation\"\r\n    aria-label=\"pagination\"\r\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n    {...props}\r\n  />\r\n);\r\n\r\nconst PaginationContent = React.forwardRef<HTMLUListElement, React.ComponentProps<\"ul\">>(\r\n  ({ className, ...props }, ref) => (\r\n    <ul ref={ref} className={cn(\"flex flex-row items-center gap-1\", className)} {...props} />\r\n  ),\r\n);\r\nPaginationContent.displayName = \"PaginationContent\";\r\n\r\nconst PaginationItem = React.forwardRef<HTMLLIElement, React.ComponentProps<\"li\">>(\r\n  ({ className, ...props }, ref) => <li ref={ref} className={cn(\"\", className)} {...props} />,\r\n);\r\nPaginationItem.displayName = \"PaginationItem\";\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean;\r\n  size?: VariantProps<typeof buttonVariants>[\"size\"];\r\n} & React.ComponentProps<\"a\">;\r\n\r\nconst PaginationLink = ({ className, isActive, size = \"icon\", ...props }: PaginationLinkProps) => (\r\n  <a\r\n    aria-current={isActive ? \"page\" : undefined}\r\n    className={cn(\r\n      buttonVariants({\r\n        variant: isActive ? \"outline\" : \"ghost\",\r\n        size,\r\n      }),\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nPaginationLink.displayName = \"PaginationLink\";\r\n\r\nconst PaginationPrevious = ({\r\n  className,\r\n  size,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to previous page\"\r\n    className={cn(\"gap-1 pl-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <ChevronLeft className=\"h-4 w-4\" />\r\n    {/* <span>Previous</span> */}\r\n  </PaginationLink>\r\n);\r\nPaginationPrevious.displayName = \"PaginationPrevious\";\r\n\r\nconst PaginationNext = ({\r\n  className,\r\n  size,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink aria-label=\"Go to next page\" className={cn(\"gap-1 pr-2.5\", className)} {...props}>\r\n    {/* <span>Next</span> */}\r\n    <ChevronRight className=\"h-4 w-4\" />\r\n  </PaginationLink>\r\n);\r\nPaginationNext.displayName = \"PaginationNext\";\r\n\r\nconst PaginationEllipsis = ({ className, ...props }: React.ComponentProps<\"span\">) => (\r\n  <span\r\n    aria-hidden\r\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"h-4 w-4\" />\r\n    <span className=\"sr-only\">More pages</span>\r\n  </span>\r\n);\r\nPaginationEllipsis.displayName = \"PaginationEllipsis\";\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AAAA;AAEA;AACA;;;;;;AAGA,MAAM,aAAa,CAAC,EAAE,SAAS,EAAE,GAAG,OAAoC,iBACtE,8OAAC;QACC,MAAK;QACL,cAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAIb,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAG,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QAAa,GAAG,KAAK;;;;;;AAGzF,kBAAkB,WAAW,GAAG;AAEhC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAAQ,8OAAC;QAAG,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QAAa,GAAG,KAAK;;;;;;AAEzF,eAAe,WAAW,GAAG;AAO7B,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,MAAM,EAAE,GAAG,OAA4B,iBAC3F,8OAAC;QACC,gBAAc,WAAW,SAAS;QAClC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,YAAY;YAChC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,IAAI,EACJ,GAAG,OACyC,iBAC5C,8OAAC;QACC,cAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAI3B,mBAAmB,WAAW,GAAG;AAEjC,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,IAAI,EACJ,GAAG,OACyC,iBAC5C,8OAAC;QAAe,cAAW;QAAkB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAAa,GAAG,KAAK;kBAE9F,cAAA,8OAAC,sNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;;;;;;AAG5B,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAqC,iBAC/E,8OAAC;QACC,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-3 shadow-sm\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return <div data-slot=\"card-content\" className={cn(\"px-6\", className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { buttonVariants } from \"@/components/ui/button\";\r\n\r\nfunction AlertDialog({ ...props }: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />;\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />;\r\n}\r\n\r\nfunction AlertDialogPortal({ ...props }: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />;\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className,\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  );\r\n}\r\n\r\nfunction AlertDialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return <AlertDialogPrimitive.Action className={cn(buttonVariants(), className)} {...props} />;\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EAAE,GAAG,OAA+D;IACvF,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBAAO,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AACjF;AAEA,SAAS,kBAAkB,EAAE,GAAG,OAAiE;IAC/F,qBAAO,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAC/E;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC7E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC7E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;QACvE,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,2KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBAAO,8OAAC,2KAAA,CAAA,SAA2B;QAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAAa,GAAG,KAAK;;;;;;AAC3F;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:pointer-events-none\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)} {...props} />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className)}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg leading-none font-semibold tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-muted-foreground text-sm\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAA6C,iBACjF,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QAAa,GAAG,KAAK;;;;;;AAEhG,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAA6C,iBACjF,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction Label({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAyD;IACtF,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1402, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/app/%28features%29/workflows/components/WorkflowCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { memo, useState } from \"react\";\r\nimport { format } from \"date-fns\";\r\nimport { Calendar, Trash2, MoreHorizontal, Pencil, Workflow } from \"lucide-react\";\r\nimport { WorkflowDetails, WorkflowSummary, deleteWorkflow, updateWorkflowMetadata } from \"../api\";\r\nimport { Card, CardHeader, CardTitle, CardDescription } from \"@/components/ui/card\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface WorkflowCardProps {\r\n  workflow: WorkflowDetails | WorkflowSummary;\r\n  onClick: (workflow: WorkflowDetails | WorkflowSummary) => void;\r\n  onDelete?: (workflowId: string) => void;\r\n}\r\n\r\n// Format date for display in \"Month DD, YYYY\" format\r\nconst formatDate = (dateString: string) => {\r\n  try {\r\n    return format(new Date(dateString), \"MMM d, yyyy\");\r\n  } catch (e) {\r\n    return \"Unknown date\";\r\n  }\r\n};\r\n\r\n// Get status display properties\r\nconst getStatusProperties = (status: string | undefined) => {\r\n  if (!status) return { label: \"Inactive\", active: false };\r\n\r\n  const isActive = status.toLowerCase() === \"active\";\r\n  return {\r\n    label: isActive ? \"Active\" : \"Inactive\",\r\n    active: isActive,\r\n  };\r\n};\r\n\r\nconst WorkflowCard: React.FC<WorkflowCardProps> = ({ workflow, onClick }) => {\r\n  // Check if the workflow is a WorkflowSummary (has workflow property) or a direct WorkflowDetails\r\n  const workflowData = \"workflow\" in workflow ? workflow.workflow : workflow;\r\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);\r\n  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [editTitle, setEditTitle] = useState(workflowData?.name || \"\");\r\n  const [editDescription, setEditDescription] = useState(workflowData?.description || \"\");\r\n\r\n  // Handle delete menu item click\r\n  const handleDeleteClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation(); // Prevent card click event\r\n    setIsDeleteDialogOpen(true);\r\n  };\r\n\r\n  // Handle edit menu item click\r\n  const handleEditClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation(); // Prevent card click event\r\n    setEditTitle(workflowData?.name || \"\");\r\n    setEditDescription(workflowData?.description || \"\");\r\n    setIsEditDialogOpen(true);\r\n  };\r\n\r\n  // Handle delete confirmation\r\n  const handleDeleteConfirm = async () => {\r\n    if (!workflowData?.id) return;\r\n\r\n    setIsDeleting(true);\r\n    try {\r\n      const result = await deleteWorkflow(workflowData.id);\r\n\r\n      if (result.success) {\r\n        toast.success(result.message || \"Workflow deleted successfully\");\r\n        // Refresh the page after a short delay to show the updated list\r\n        setTimeout(() => {\r\n          window.location.reload();\r\n        }, 1000);\r\n      } else {\r\n        toast.error(\"Failed to delete workflow\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting workflow:\", error);\r\n      toast.error(\r\n        error instanceof Error ? error.message : \"An error occurred while deleting the workflow\",\r\n      );\r\n    } finally {\r\n      setIsDeleting(false);\r\n      setIsDeleteDialogOpen(false);\r\n    }\r\n  };\r\n\r\n  // Handle update workflow\r\n  const handleUpdateWorkflow = async () => {\r\n    if (!workflowData?.id) return;\r\n\r\n    setIsUpdating(true);\r\n    try {\r\n      await updateWorkflowMetadata(workflowData.id, {\r\n        name: editTitle,\r\n        description: editDescription,\r\n      });\r\n\r\n      toast.success(\"Workflow updated successfully\");\r\n\r\n      // Refresh the page after a short delay to show the updated list\r\n      setTimeout(() => {\r\n        window.location.reload();\r\n      }, 1000);\r\n    } catch (error) {\r\n      console.error(\"Error updating workflow:\", error);\r\n      toast.error(\r\n        error instanceof Error ? error.message : \"An error occurred while updating the workflow\",\r\n      );\r\n    } finally {\r\n      setIsUpdating(false);\r\n      setIsEditDialogOpen(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Card\r\n        className=\"group relative w-full cursor-pointer transition-all hover:shadow-lg\"\r\n        style={{ backgroundColor: \"#1E1E1E\" }}\r\n        onClick={() => onClick(workflow)}\r\n        onKeyDown={(e) => {\r\n          // Handle keyboard navigation - Enter or Space to select\r\n          if (e.key === \"Enter\" || e.key === \" \") {\r\n            e.preventDefault();\r\n            onClick(workflow);\r\n          }\r\n        }}\r\n        tabIndex={0}\r\n        role=\"button\"\r\n        aria-label={`Select workflow: ${workflowData?.name || \"Untitled Workflow\"}`}\r\n      >\r\n        <CardHeader className=\"relative px-3 py-3\">\r\n          {/* Kebab menu - positioned absolutely in top right corner */}\r\n          <div\r\n            className=\"absolute right-2 z-10\"\r\n            style={{ top: \"-6px\" }}\r\n            onClick={(e) => e.stopPropagation()}\r\n            role=\"menu\"\r\n            aria-label=\"Workflow options\"\r\n          >\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger className=\"rounded-full p-1.5 transition-colors hover:bg-gray-600\">\r\n                <MoreHorizontal className=\"h-4 w-4 text-white\" />\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent align=\"end\" className=\"border-brand-stroke bg-brand-card w-40\">\r\n                <DropdownMenuItem\r\n                  className=\"text-brand-primary-font hover:bg-brand-card-hover flex cursor-pointer items-center gap-2 py-2\"\r\n                  onClick={handleEditClick}\r\n                >\r\n                  <Pencil className=\"h-4 w-4\" />\r\n                  <span>Edit</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem\r\n                  className=\"text-brand-unpublish hover:bg-brand-card-hover flex cursor-pointer items-center gap-2 py-2\"\r\n                  onClick={handleDeleteClick}\r\n                >\r\n                  <Trash2 className=\"h-4 w-4\" />\r\n                  <span>Delete</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n\r\n          {/* Main content layout */}\r\n          <div className=\"flex items-center justify-between\">\r\n            {/* Left side: Icon and Name/Description */}\r\n            <div className=\"flex flex-1 items-center gap-3 pr-4\">\r\n              {/* Workflow Icon */}\r\n              <div className=\"bg-brand-primary/20 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full\">\r\n                <Workflow className=\"text-brand-primary h-5 w-5\" />\r\n              </div>\r\n\r\n              {/* Name and Description */}\r\n              <div className=\"min-w-0 flex-1\">\r\n                <CardTitle className=\"font-primary text-base font-semibold text-white\">\r\n                  {workflowData?.name || \"Untitled Workflow\"}\r\n                </CardTitle>\r\n                <CardDescription className=\"font-secondary text-xs text-gray-300\">\r\n                  {workflowData?.description || \"No description provided\"}\r\n                </CardDescription>\r\n                {/* Date below description */}\r\n                <div className=\"mt-1 flex items-center gap-1 text-xs text-gray-400\">\r\n                  <Calendar className=\"h-3 w-3\" />\r\n                  <span>\r\n                    {workflowData?.created_at ? formatDate(workflowData.created_at) : \"Unknown\"}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right side: Status Toggle */}\r\n            <div className=\"flex flex-shrink-0 items-center\">\r\n              <div\r\n                className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${\r\n                  getStatusProperties(workflowData?.status).active\r\n                    ? \"bg-[rgba(123,90,255,1)]\"\r\n                    : \"bg-gray-500\"\r\n                }`}\r\n                role=\"switch\"\r\n                aria-checked={getStatusProperties(workflowData?.status).active}\r\n                aria-label={`Toggle workflow status: currently ${getStatusProperties(workflowData?.status).label}`}\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  // Status toggle functionality would go here\r\n                }}\r\n              >\r\n                <span\r\n                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\r\n                    getStatusProperties(workflowData?.status).active\r\n                      ? \"translate-x-5\"\r\n                      : \"translate-x-1\"\r\n                  }`}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n      </Card>\r\n\r\n      {/* Delete confirmation dialog */}\r\n      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>\r\n        <AlertDialogContent className=\"border-brand-stroke bg-brand-card\">\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle className=\"font-primary text-brand-primary-font\">\r\n              Delete Workflow\r\n            </AlertDialogTitle>\r\n            <AlertDialogDescription className=\"font-secondary text-brand-secondary-font\">\r\n              Are you sure you want to delete this workflow? This action cannot be undone.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel\r\n              disabled={isDeleting}\r\n              className=\"border-brand-stroke text-brand-primary-font hover:bg-brand-clicked\"\r\n            >\r\n              Cancel\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleDeleteConfirm}\r\n              disabled={isDeleting}\r\n              className=\"bg-brand-unpublish text-brand-white-text hover:bg-brand-unpublish/90\"\r\n            >\r\n              {isDeleting ? \"Deleting...\" : \"Delete\"}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Edit workflow dialog */}\r\n      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>\r\n        <DialogContent className=\"border-brand-stroke bg-brand-card sm:max-w-[500px]\">\r\n          <DialogHeader>\r\n            <DialogTitle className=\"font-primary text-brand-primary-font\">\r\n              Edit Workflow\r\n            </DialogTitle>\r\n            <DialogDescription className=\"font-secondary text-brand-secondary-font\">\r\n              Update the title and description of your workflow.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n\r\n          <div className=\"grid gap-4 py-4\">\r\n            <div className=\"grid gap-2\">\r\n              <Label htmlFor=\"title\" className=\"text-brand-primary-font\">\r\n                Title\r\n              </Label>\r\n              <Input\r\n                id=\"title\"\r\n                value={editTitle}\r\n                onChange={(e) => setEditTitle(e.target.value)}\r\n                className=\"border-brand-stroke bg-brand-card-hover text-brand-primary-font\"\r\n                placeholder=\"Enter workflow title\"\r\n              />\r\n            </div>\r\n            <div className=\"grid gap-2\">\r\n              <Label htmlFor=\"description\" className=\"text-brand-primary-font\">\r\n                Description\r\n              </Label>\r\n              <Textarea\r\n                id=\"description\"\r\n                value={editDescription}\r\n                onChange={(e) => setEditDescription(e.target.value)}\r\n                className=\"border-brand-stroke bg-brand-card-hover text-brand-primary-font min-h-[100px]\"\r\n                placeholder=\"Enter workflow description\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <DialogFooter>\r\n            <Button\r\n              variant=\"outline\"\r\n              onClick={() => setIsEditDialogOpen(false)}\r\n              className=\"border-brand-stroke text-brand-primary-font hover:bg-brand-clicked\"\r\n              disabled={isUpdating}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              onClick={handleUpdateWorkflow}\r\n              className=\"bg-brand-primary text-brand-white-text hover:bg-brand-primary/90\"\r\n              disabled={isUpdating}\r\n            >\r\n              {isUpdating ? \"Saving...\" : \"Save changes\"}\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n};\r\n\r\n// Memoize the component to prevent unnecessary re-renders\r\nexport default memo(WorkflowCard);\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAUA;AAQA;AAMA;AACA;AACA;AACA;AACA;AAnCA;;;;;;;;;;;;;;;AA2CA,qDAAqD;AACrD,MAAM,aAAa,CAAC;IAClB,IAAI;QACF,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa;IACtC,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF;AAEA,gCAAgC;AAChC,MAAM,sBAAsB,CAAC;IAC3B,IAAI,CAAC,QAAQ,OAAO;QAAE,OAAO;QAAY,QAAQ;IAAM;IAEvD,MAAM,WAAW,OAAO,WAAW,OAAO;IAC1C,OAAO;QACL,OAAO,WAAW,WAAW;QAC7B,QAAQ;IACV;AACF;AAEA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;IACtE,iGAAiG;IACjG,MAAM,eAAe,cAAc,WAAW,SAAS,QAAQ,GAAG;IAClE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,QAAQ;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,eAAe;IAEpF,gCAAgC;IAChC,MAAM,oBAAoB,CAAC;QACzB,EAAE,eAAe,IAAI,2BAA2B;QAChD,sBAAsB;IACxB;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,CAAC;QACvB,EAAE,eAAe,IAAI,2BAA2B;QAChD,aAAa,cAAc,QAAQ;QACnC,mBAAmB,cAAc,eAAe;QAChD,oBAAoB;IACtB;IAEA,6BAA6B;IAC7B,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc,IAAI;QAEvB,cAAc;QACd,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,EAAE;YAEnD,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,IAAI;gBAChC,gEAAgE;gBAChE,WAAW;oBACT,OAAO,QAAQ,CAAC,MAAM;gBACxB,GAAG;YACL,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE7C,SAAU;YACR,cAAc;YACd,sBAAsB;QACxB;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,IAAI,CAAC,cAAc,IAAI;QAEvB,cAAc;QACd,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,EAAE,EAAE;gBAC5C,MAAM;gBACN,aAAa;YACf;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,gEAAgE;YAChE,WAAW;gBACT,OAAO,QAAQ,CAAC,MAAM;YACxB,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE7C,SAAU;YACR,cAAc;YACd,oBAAoB;QACtB;IACF;IAEA,qBACE;;0BACE,8OAAC,gIAAA,CAAA,OAAI;gBACH,WAAU;gBACV,OAAO;oBAAE,iBAAiB;gBAAU;gBACpC,SAAS,IAAM,QAAQ;gBACvB,WAAW,CAAC;oBACV,wDAAwD;oBACxD,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;wBACtC,EAAE,cAAc;wBAChB,QAAQ;oBACV;gBACF;gBACA,UAAU;gBACV,MAAK;gBACL,cAAY,CAAC,iBAAiB,EAAE,cAAc,QAAQ,qBAAqB;0BAE3E,cAAA,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCAEpB,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,KAAK;4BAAO;4BACrB,SAAS,CAAC,IAAM,EAAE,eAAe;4BACjC,MAAK;4BACL,cAAW;sCAEX,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,WAAU;kDAC7B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;kDAE5B,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,WAAU;gDACV,SAAS;;kEAET,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,WAAU;gDACV,SAAS;;kEAET,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAItB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,cAAc,QAAQ;;;;;;8DAEzB,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,cAAc,eAAe;;;;;;8DAGhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;sEACE,cAAc,aAAa,WAAW,aAAa,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;;8CAO1E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAW,CAAC,yEAAyE,EACnF,oBAAoB,cAAc,QAAQ,MAAM,GAC5C,4BACA,eACJ;wCACF,MAAK;wCACL,gBAAc,oBAAoB,cAAc,QAAQ,MAAM;wCAC9D,cAAY,CAAC,kCAAkC,EAAE,oBAAoB,cAAc,QAAQ,KAAK,EAAE;wCAClG,SAAS,CAAC;4CACR,EAAE,eAAe;wCACjB,4CAA4C;wCAC9C;kDAEA,cAAA,8OAAC;4CACC,WAAW,CAAC,0EAA0E,EACpF,oBAAoB,cAAc,QAAQ,MAAM,GAC5C,kBACA,iBACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAoB,cAAc;0BACnD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;oBAAC,WAAU;;sCAC5B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAAuC;;;;;;8CAGnE,8OAAC,2IAAA,CAAA,yBAAsB;oCAAC,WAAU;8CAA2C;;;;;;;;;;;;sCAI/E,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;8CAAuC;;;;;;8CAG9D,8OAAC,kIAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAA2C;;;;;;;;;;;;sCAK1E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAQ,WAAU;sDAA0B;;;;;;sDAG3D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC5C,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;sDAA0B;;;;;;sDAGjE,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAClD,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAKlB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,WAAU;oCACV,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;oCACV,UAAU;8CAET,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;AAO1C;qDAGe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1972, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/app/%28features%29/workflows/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { getClientAccessToken } from \"@/lib/clientCookies\";\r\nimport {\r\n  fetchWorkflowsByUser,\r\n  createEmptyWorkflow,\r\n  WorkflowSummary,\r\n  WorkflowDetails,\r\n} from \"../api\";\r\nimport { useUserStore } from \"@/store/userStore\";\r\nimport {\r\n  Card,\r\n  CardHeader,\r\n  CardTitle,\r\n  CardDescription,\r\n  CardContent,\r\n  CardFooter,\r\n} from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport {\r\n  Pagination,\r\n  PaginationContent,\r\n  Pa<PERSON>ationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"@/components/ui/pagination\";\r\nimport {\r\n  Workflow,\r\n  Plus,\r\n  Search,\r\n  AlertCircle,\r\n  Loader2,\r\n  ChevronDown,\r\n  FileEdit,\r\n  File,\r\n  FileUp,\r\n} from \"lucide-react\";\r\nimport WorkflowCard from \"../components/WorkflowCard\";\r\nimport Image from \"next/image\";\r\n\r\nexport default function WorkflowsListPage() {\r\n  const router = useRouter();\r\n  const user = useUserStore((state) => state.user);\r\n\r\n  const [workflows, setWorkflows] = useState<WorkflowDetails[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [sortOption, setSortOption] = useState<string>(\"updated_desc\");\r\n  const [searchTerm, setSearchTerm] = useState<string>(\"\");\r\n  const [isCreatingWorkflow, setIsCreatingWorkflow] = useState(false);\r\n\r\n  // Pagination state\r\n  const [currentPage, setCurrentPage] = useState<number>(1);\r\n  const [totalPages, setTotalPages] = useState<number>(1);\r\n  const [pageSize, setPageSize] = useState<number>(10);\r\n\r\n  // Check authentication status on mount\r\n  useEffect(() => {\r\n    const checkAuth = async () => {\r\n      try {\r\n        // Get access token from client-side cookie\r\n        const cookieToken = getClientAccessToken();\r\n\r\n        // If no access token in cookie or store, redirect to login\r\n        if (!cookieToken && (!user || !user.accessToken)) {\r\n          console.error(\"Authentication error: No access token available\");\r\n          setError(\"User not authenticated\");\r\n          setIsLoading(false);\r\n\r\n          // Redirect to login page after a short delay\r\n          setTimeout(() => {\r\n            window.location.href = \"/login\";\r\n          }, 2000);\r\n\r\n          return;\r\n        }\r\n\r\n        // If we have a token in cookie but not in store, update the store\r\n        if (cookieToken && (!user || !user.accessToken)) {\r\n          console.log(\"Found token in cookie but not in store, updating store\");\r\n          // This is a temporary fix - ideally we would fetch user details here\r\n          useUserStore.getState().setUser({\r\n            accessToken: cookieToken,\r\n            // We don't have other user details, but at least we have the token\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking authentication:\", error);\r\n        setError(\"Authentication error\");\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    checkAuth();\r\n  }, [user]);\r\n\r\n  // Fetch workflows when component mounts or pagination changes\r\n  useEffect(() => {\r\n    const fetchWorkflows = async () => {\r\n      // Get token from store or cookie\r\n      const tokenFromStore = user?.accessToken;\r\n      const tokenFromCookie = getClientAccessToken();\r\n      const accessToken = tokenFromStore || tokenFromCookie;\r\n\r\n      // Skip if no access token available\r\n      if (!accessToken) {\r\n        console.error(\"No access token available for API request\");\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setIsLoading(true);\r\n        setError(null);\r\n\r\n        // Pass the access token explicitly to the API function\r\n        const response = await fetchWorkflowsByUser(currentPage, pageSize, accessToken);\r\n\r\n        setWorkflows(response?.data);\r\n\r\n        // Update pagination state\r\n        if (response.metadata) {\r\n          setTotalPages(response.metadata.totalPages || 1);\r\n          setCurrentPage(response.metadata.currentPage || 1);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch workflows:\", err);\r\n        // Provide more specific error messages based on the error\r\n        if (err instanceof Error) {\r\n          if (err.message.includes(\"401\") || err.message.includes(\"403\")) {\r\n            setError(\"Authentication failed. Please log in again.\");\r\n          } else if (err.message.includes(\"404\")) {\r\n            setError(\"No workflows found for this user.\");\r\n          } else if (err.message.includes(\"500\")) {\r\n            setError(\"Server error. Please try again later.\");\r\n          } else {\r\n            setError(`Failed to load workflows: ${err.message}`);\r\n          }\r\n        } else {\r\n          setError(\"Failed to load workflows. Please try again later.\");\r\n        }\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchWorkflows();\r\n  }, [user, currentPage, pageSize]);\r\n\r\n  // Helper function to get workflow data regardless of structure\r\n  const getWorkflowData = (data: any): WorkflowDetails => {\r\n    // If it's a WorkflowSummary (has workflow property), return the nested workflow\r\n    if (\"workflow\" in data && data.workflow) {\r\n      return data.workflow as WorkflowDetails;\r\n    }\r\n    // Otherwise, it's already a WorkflowDetails\r\n    return data as WorkflowDetails;\r\n  };\r\n\r\n  // Sort workflows based on selected option\r\n  const sortedWorkflows = useMemo(() => {\r\n    if (!workflows) return [];\r\n\r\n    // Filter by search term if provided\r\n    let filtered = workflows;\r\n    if (searchTerm) {\r\n      const term = searchTerm.toLowerCase();\r\n      filtered = workflows.filter((wf) => {\r\n        const workflowData = getWorkflowData(wf);\r\n        return (\r\n          workflowData.name?.toLowerCase().includes(term) ||\r\n          (workflowData.description && workflowData.description.toLowerCase().includes(term))\r\n        );\r\n      });\r\n    }\r\n\r\n    // Sort based on selected option\r\n    return [...filtered].sort((a, b) => {\r\n      const aData = getWorkflowData(a);\r\n      const bData = getWorkflowData(b);\r\n\r\n      switch (sortOption) {\r\n        case \"updated_desc\":\r\n          return (\r\n            new Date(bData.updated_at || \"\").getTime() - new Date(aData.updated_at || \"\").getTime()\r\n          );\r\n        case \"updated_asc\":\r\n          return (\r\n            new Date(aData.updated_at || \"\").getTime() - new Date(bData.updated_at || \"\").getTime()\r\n          );\r\n        case \"name_asc\":\r\n          return (aData.name || \"\").localeCompare(bData.name || \"\");\r\n        case \"name_desc\":\r\n          return (bData.name || \"\").localeCompare(aData.name || \"\");\r\n        default:\r\n          return (\r\n            new Date(bData.updated_at || \"\").getTime() - new Date(aData.updated_at || \"\").getTime()\r\n          );\r\n      }\r\n    });\r\n  }, [workflows, sortOption, searchTerm]);\r\n\r\n  // Handle creating a new workflow\r\n  const handleCreateWorkflow = async () => {\r\n    try {\r\n      setIsCreatingWorkflow(true);\r\n      const newWorkflow = await createEmptyWorkflow();\r\n      console.log(\"Created new workflow:\", newWorkflow);\r\n      // Redirect to canvas page with the new workflow ID\r\n      // router.push(`/workflows/${newWorkflow.workflow_id}`);\r\n      router.push(`/?workflow_id=${newWorkflow.workflow_id}`);\r\n    } catch (err) {\r\n      console.error(\"Failed to create workflow:\", err);\r\n      setError(\"Failed to create a new workflow. Please try again.\");\r\n      setIsCreatingWorkflow(false);\r\n    }\r\n  };\r\n\r\n  // Handle creating workflow from template (placeholder)\r\n  const handleCreateFromTemplate = () => {\r\n    // TODO: Implement template selection functionality\r\n    console.log(\"Create from template clicked - functionality to be implemented\");\r\n  };\r\n\r\n  // Handle importing workflow file (placeholder)\r\n  const handleImportFile = () => {\r\n    // TODO: Implement file import functionality\r\n    console.log(\"Import file clicked - functionality to be implemented\");\r\n  };\r\n\r\n  // Memoize the handleSelectWorkflow function to prevent unnecessary re-renders\r\n  const handleSelectWorkflow = useCallback(\r\n    (workflow: WorkflowSummary | WorkflowDetails) => {\r\n      // Check if the workflow is a WorkflowSummary (has workflow property) or a direct WorkflowDetails\r\n      const workflowId =\r\n        \"workflow\" in workflow && workflow.workflow\r\n          ? workflow.workflow.id\r\n          : (workflow as WorkflowDetails).id;\r\n\r\n      if (workflowId) {\r\n        // router.push(`/workflows/${workflowId}`);\r\n        router.push(`/?workflow_id=${workflowId}`);\r\n      }\r\n    },\r\n    [router],\r\n  );\r\n\r\n  return (\r\n    <main className=\"bg-background min-h-screen\">\r\n      {/* Header */}\r\n      <div className=\"bg-brand-header-bg text-brand-white-text p-6 shadow-md\">\r\n        <div\r\n          className=\"container\"\r\n          style={{\r\n            maxWidth: \"100%\",\r\n          }}\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Image\r\n                  src=\"/wflogo_white.svg\"\r\n                  alt=\"Workflow Builder Logo\"\r\n                  width={120}\r\n                  height={30}\r\n                  className=\"h-8 w-auto\"\r\n                />\r\n              </div>\r\n            </div>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button\r\n                  disabled={isCreatingWorkflow}\r\n                  className=\"bg-brand-primary hover:bg-brand-primary/90 text-white\"\r\n                >\r\n                  {isCreatingWorkflow ? (\r\n                    <>\r\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                      Creating...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <Plus className=\"mr-2 h-4 w-4\" />\r\n                      Create New Workflow\r\n                      <ChevronDown className=\"ml-2 h-4 w-4\" />\r\n                    </>\r\n                  )}\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent align=\"end\" className=\"w-56\">\r\n                <DropdownMenuItem\r\n                  onClick={handleCreateWorkflow}\r\n                  disabled={isCreatingWorkflow}\r\n                  className=\"flex items-center gap-2 py-2\"\r\n                >\r\n                  <FileEdit className=\"h-4 w-4\" />\r\n                  <span>Create from blank</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem\r\n                  onClick={handleCreateFromTemplate}\r\n                  disabled={isCreatingWorkflow}\r\n                  className=\"flex items-center gap-2 py-2\"\r\n                >\r\n                  <File className=\"h-4 w-4\" />\r\n                  <span>Create from template</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem\r\n                  onClick={handleImportFile}\r\n                  disabled={isCreatingWorkflow}\r\n                  className=\"flex items-center gap-2 py-2\"\r\n                >\r\n                  <FileUp className=\"h-4 w-4\" />\r\n                  <span>Import file</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main content */}\r\n      <div\r\n        className=\"bg-brand-background container px-4 py-8\"\r\n        style={{\r\n          maxWidth: \"100%\",\r\n          background: \"black\",\r\n          padding: \"20px 100px\",\r\n        }}\r\n      >\r\n        {/* Filters and search */}\r\n        <div className=\"mb-6 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center\">\r\n          <div className=\"relative w-full md:w-64\">\r\n            <Search\r\n              className=\"text-brand-secondary-font absolute top-3 left-3 h-4 w-4\"\r\n              aria-hidden=\"true\"\r\n            />\r\n            <Input\r\n              placeholder=\"Search workflows...\"\r\n              className=\"border-brand-stroke focus-visible:ring-brand-primary/20 pl-10\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n              aria-label=\"Search workflows\"\r\n              id=\"workflow-search\"\r\n              name=\"workflow-search\"\r\n            />\r\n          </div>\r\n          <div className=\"flex items-center gap-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"text-brand-secondary-font font-secondary text-sm\">Sort by:</span>\r\n              <Select value={sortOption} onValueChange={setSortOption}>\r\n                <SelectTrigger className=\"border-brand-stroke focus:ring-brand-primary/20 w-[180px]\">\r\n                  <SelectValue placeholder=\"Sort by\" />\r\n                </SelectTrigger>\r\n                <SelectContent className=\"border-brand-border-color bg-brand-card\">\r\n                  <SelectItem value=\"updated_desc\">Latest Update</SelectItem>\r\n                  <SelectItem value=\"updated_asc\">Oldest Update</SelectItem>\r\n                  <SelectItem value=\"name_asc\">Name (A-Z)</SelectItem>\r\n                  <SelectItem value=\"name_desc\">Name (Z-A)</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"text-brand-secondary-font font-secondary text-sm\">Show:</span>\r\n              <Select\r\n                value={pageSize.toString()}\r\n                onValueChange={(value) => {\r\n                  setPageSize(Number(value));\r\n                  setCurrentPage(1); // Reset to first page when changing page size\r\n                }}\r\n              >\r\n                <SelectTrigger className=\"border-brand-stroke focus:ring-brand-primary/20 w-[80px]\">\r\n                  <SelectValue placeholder=\"Page size\" />\r\n                </SelectTrigger>\r\n                <SelectContent className=\"border-brand-border-color bg-brand-card\">\r\n                  <SelectItem value=\"5\">5</SelectItem>\r\n                  <SelectItem value=\"10\">10</SelectItem>\r\n                  <SelectItem value=\"20\">20</SelectItem>\r\n                  <SelectItem value=\"50\">50</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Workflows grid */}\r\n        {isLoading ? (\r\n          <div className=\"flex h-64 items-center justify-center\">\r\n            <Loader2 className=\"text-brand-primary h-8 w-8 animate-spin\" />\r\n            <span className=\"font-primary ml-2 text-lg\">Loading workflows...</span>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"flex h-64 flex-col items-center justify-center text-center\">\r\n            <AlertCircle className=\"text-brand-unpublish mb-4 h-12 w-12\" />\r\n            <h3 className=\"font-primary mb-2 text-xl font-semibold\">Failed to Load Workflows</h3>\r\n            <p className=\"text-brand-secondary-font mb-4\">{error}</p>\r\n            <div className=\"flex gap-4\">\r\n              <Button\r\n                onClick={() => window.location.reload()}\r\n                className=\"bg-brand-primary hover:bg-brand-primary/90 text-white\"\r\n              >\r\n                Try Again\r\n              </Button>\r\n              {error.includes(\"authenticated\") && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  onClick={() => (window.location.href = \"/login\")}\r\n                  className=\"border-brand-border-color text-brand-primary hover:bg-brand-clicked\"\r\n                >\r\n                  Go to Login\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ) : sortedWorkflows.length === 0 ? (\r\n          <div className=\"flex h-64 flex-col items-center justify-center text-center\">\r\n            <Workflow className=\"text-brand-secondary mb-4 h-12 w-12\" />\r\n            <h3 className=\"font-primary mb-2 text-xl font-semibold\">No Workflows Found</h3>\r\n            <p className=\"text-brand-secondary-font mb-4\">\r\n              {searchTerm\r\n                ? \"No workflows match your search criteria.\"\r\n                : \"You don't have any workflows yet. Create your first workflow to get started.\"}\r\n            </p>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button\r\n                  disabled={isCreatingWorkflow}\r\n                  className=\"bg-brand-primary hover:bg-brand-primary/90 text-white\"\r\n                >\r\n                  {isCreatingWorkflow ? (\r\n                    <>\r\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                      Creating...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <Plus className=\"mr-2 h-4 w-4\" />\r\n                      Create New Workflow\r\n                      <ChevronDown className=\"ml-2 h-4 w-4\" />\r\n                    </>\r\n                  )}\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent align=\"center\" className=\"w-56\">\r\n                <DropdownMenuItem\r\n                  onClick={handleCreateWorkflow}\r\n                  disabled={isCreatingWorkflow}\r\n                  className=\"flex items-center gap-2 py-2\"\r\n                >\r\n                  <FileEdit className=\"h-4 w-4\" />\r\n                  <span>Create from blank</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem\r\n                  onClick={handleCreateFromTemplate}\r\n                  disabled={isCreatingWorkflow}\r\n                  className=\"flex items-center gap-2 py-2\"\r\n                >\r\n                  <File className=\"h-4 w-4\" />\r\n                  <span>Create from template</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem\r\n                  onClick={handleImportFile}\r\n                  disabled={isCreatingWorkflow}\r\n                  className=\"flex items-center gap-2 py-2\"\r\n                >\r\n                  <FileUp className=\"h-4 w-4\" />\r\n                  <span>Import file</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n        ) : (\r\n          <div className=\"flex flex-col gap-6\">\r\n            <div className=\"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3\">\r\n              {sortedWorkflows.map((workflow) => {\r\n                // Generate a key that works for both data structures\r\n                const workflowData = getWorkflowData(workflow);\r\n                const key = workflowData.id || Math.random().toString();\r\n\r\n                return (\r\n                  <WorkflowCard key={key} workflow={workflow} onClick={handleSelectWorkflow} />\r\n                );\r\n              })}\r\n            </div>\r\n\r\n            {/* Pagination */}\r\n            {totalPages > 1 && (\r\n              <Pagination className=\"mt-6\" aria-label=\"Workflow pagination\">\r\n                <PaginationContent>\r\n                  <PaginationItem>\r\n                    <PaginationPrevious\r\n                      size=\"default\"\r\n                      onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}\r\n                      className={`${currentPage <= 1 ? \"pointer-events-none opacity-50\" : \"\"} text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke`}\r\n                      aria-disabled={currentPage <= 1}\r\n                    />\r\n                  </PaginationItem>\r\n\r\n                  {/* First page */}\r\n                  {currentPage > 2 && (\r\n                    <PaginationItem>\r\n                      <PaginationLink\r\n                        size=\"default\"\r\n                        onClick={() => setCurrentPage(1)}\r\n                        className=\"text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke\"\r\n                      >\r\n                        1\r\n                      </PaginationLink>\r\n                    </PaginationItem>\r\n                  )}\r\n\r\n                  {/* Ellipsis if needed */}\r\n                  {currentPage > 3 && (\r\n                    <PaginationItem>\r\n                      <PaginationEllipsis className=\"text-brand-secondary-font\" />\r\n                    </PaginationItem>\r\n                  )}\r\n\r\n                  {/* Previous page if not first */}\r\n                  {currentPage > 1 && (\r\n                    <PaginationItem>\r\n                      <PaginationLink\r\n                        size=\"default\"\r\n                        onClick={() => setCurrentPage(currentPage - 1)}\r\n                        className=\"text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke\"\r\n                      >\r\n                        {currentPage - 1}\r\n                      </PaginationLink>\r\n                    </PaginationItem>\r\n                  )}\r\n\r\n                  {/* Current page */}\r\n                  <PaginationItem>\r\n                    <PaginationLink\r\n                      size=\"default\"\r\n                      isActive\r\n                      className=\"brand-gradient-indicator text-brand-white-text border-none\"\r\n                    >\r\n                      {currentPage}\r\n                    </PaginationLink>\r\n                  </PaginationItem>\r\n\r\n                  {/* Next page if not last */}\r\n                  {currentPage < totalPages && (\r\n                    <PaginationItem>\r\n                      <PaginationLink\r\n                        size=\"default\"\r\n                        onClick={() => setCurrentPage(currentPage + 1)}\r\n                        className=\"text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke\"\r\n                      >\r\n                        {currentPage + 1}\r\n                      </PaginationLink>\r\n                    </PaginationItem>\r\n                  )}\r\n\r\n                  {/* Ellipsis if needed */}\r\n                  {currentPage < totalPages - 2 && (\r\n                    <PaginationItem>\r\n                      <PaginationEllipsis className=\"text-brand-secondary-font\" />\r\n                    </PaginationItem>\r\n                  )}\r\n\r\n                  {/* Last page */}\r\n                  {currentPage < totalPages - 1 && (\r\n                    <PaginationItem>\r\n                      <PaginationLink\r\n                        size=\"default\"\r\n                        onClick={() => setCurrentPage(totalPages)}\r\n                        className=\"text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke\"\r\n                      >\r\n                        {totalPages}\r\n                      </PaginationLink>\r\n                    </PaginationItem>\r\n                  )}\r\n\r\n                  <PaginationItem>\r\n                    <PaginationNext\r\n                      size=\"default\"\r\n                      onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}\r\n                      className={`${currentPage >= totalPages ? \"pointer-events-none opacity-50\" : \"\"} text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke`}\r\n                      aria-disabled={currentPage >= totalPages}\r\n                    />\r\n                  </PaginationItem>\r\n                </PaginationContent>\r\n              </Pagination>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AASA;AACA;AAMA;AAOA;AAEA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAzDA;;;;;;;;;;;;;;;AA2De,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,OAAO,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IAE/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,mBAAmB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjD,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,2CAA2C;gBAC3C,MAAM,cAAc,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD;gBAEvC,2DAA2D;gBAC3D,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,KAAK,WAAW,GAAG;oBAChD,QAAQ,KAAK,CAAC;oBACd,SAAS;oBACT,aAAa;oBAEb,6CAA6C;oBAC7C,WAAW;wBACT,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB,GAAG;oBAEH;gBACF;gBAEA,kEAAkE;gBAClE,IAAI,eAAe,CAAC,CAAC,QAAQ,CAAC,KAAK,WAAW,GAAG;oBAC/C,QAAQ,GAAG,CAAC;oBACZ,qEAAqE;oBACrE,yHAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,OAAO,CAAC;wBAC9B,aAAa;oBAEf;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SAAS;gBACT,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAK;IAET,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,iCAAiC;YACjC,MAAM,iBAAiB,MAAM;YAC7B,MAAM,kBAAkB,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD;YAC3C,MAAM,cAAc,kBAAkB;YAEtC,oCAAoC;YACpC,IAAI,CAAC,aAAa;gBAChB,QAAQ,KAAK,CAAC;gBACd;YACF;YAEA,IAAI;gBACF,aAAa;gBACb,SAAS;gBAET,uDAAuD;gBACvD,MAAM,WAAW,MAAM,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,UAAU;gBAEnE,aAAa,UAAU;gBAEvB,0BAA0B;gBAC1B,IAAI,SAAS,QAAQ,EAAE;oBACrB,cAAc,SAAS,QAAQ,CAAC,UAAU,IAAI;oBAC9C,eAAe,SAAS,QAAQ,CAAC,WAAW,IAAI;gBAClD;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,0DAA0D;gBAC1D,IAAI,eAAe,OAAO;oBACxB,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ;wBAC9D,SAAS;oBACX,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ;wBACtC,SAAS;oBACX,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ;wBACtC,SAAS;oBACX,OAAO;wBACL,SAAS,CAAC,0BAA0B,EAAE,IAAI,OAAO,EAAE;oBACrD;gBACF,OAAO;oBACL,SAAS;gBACX;YACF,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;QAAM;QAAa;KAAS;IAEhC,+DAA+D;IAC/D,MAAM,kBAAkB,CAAC;QACvB,gFAAgF;QAChF,IAAI,cAAc,QAAQ,KAAK,QAAQ,EAAE;YACvC,OAAO,KAAK,QAAQ;QACtB;QACA,4CAA4C;QAC5C,OAAO;IACT;IAEA,0CAA0C;IAC1C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,oCAAoC;QACpC,IAAI,WAAW;QACf,IAAI,YAAY;YACd,MAAM,OAAO,WAAW,WAAW;YACnC,WAAW,UAAU,MAAM,CAAC,CAAC;gBAC3B,MAAM,eAAe,gBAAgB;gBACrC,OACE,aAAa,IAAI,EAAE,cAAc,SAAS,SACzC,aAAa,WAAW,IAAI,aAAa,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;YAEjF;QACF;QAEA,gCAAgC;QAChC,OAAO;eAAI;SAAS,CAAC,IAAI,CAAC,CAAC,GAAG;YAC5B,MAAM,QAAQ,gBAAgB;YAC9B,MAAM,QAAQ,gBAAgB;YAE9B,OAAQ;gBACN,KAAK;oBACH,OACE,IAAI,KAAK,MAAM,UAAU,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,MAAM,UAAU,IAAI,IAAI,OAAO;gBAEzF,KAAK;oBACH,OACE,IAAI,KAAK,MAAM,UAAU,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,MAAM,UAAU,IAAI,IAAI,OAAO;gBAEzF,KAAK;oBACH,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,EAAE,aAAa,CAAC,MAAM,IAAI,IAAI;gBACxD,KAAK;oBACH,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,EAAE,aAAa,CAAC,MAAM,IAAI,IAAI;gBACxD;oBACE,OACE,IAAI,KAAK,MAAM,UAAU,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,MAAM,UAAU,IAAI,IAAI,OAAO;YAE3F;QACF;IACF,GAAG;QAAC;QAAW;QAAY;KAAW;IAEtC,iCAAiC;IACjC,MAAM,uBAAuB;QAC3B,IAAI;YACF,sBAAsB;YACtB,MAAM,cAAc,MAAM,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;YAC5C,QAAQ,GAAG,CAAC,yBAAyB;YACrC,mDAAmD;YACnD,wDAAwD;YACxD,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,YAAY,WAAW,EAAE;QACxD,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS;YACT,sBAAsB;QACxB;IACF;IAEA,uDAAuD;IACvD,MAAM,2BAA2B;QAC/B,mDAAmD;QACnD,QAAQ,GAAG,CAAC;IACd;IAEA,+CAA+C;IAC/C,MAAM,mBAAmB;QACvB,4CAA4C;QAC5C,QAAQ,GAAG,CAAC;IACd;IAEA,8EAA8E;IAC9E,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC;QACC,iGAAiG;QACjG,MAAM,aACJ,cAAc,YAAY,SAAS,QAAQ,GACvC,SAAS,QAAQ,CAAC,EAAE,GACpB,AAAC,SAA6B,EAAE;QAEtC,IAAI,YAAY;YACd,2CAA2C;YAC3C,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,YAAY;QAC3C;IACF,GACA;QAAC;KAAO;IAGV,qBACE,8OAAC;QAAK,WAAU;;0BAEd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,UAAU;oBACZ;8BAEA,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;0CAIhB,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,UAAU;4CACV,WAAU;sDAET,mCACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;kEAEjC,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAK/B,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,8OAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlB,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,SAAS;gBACX;;kCAGA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCACL,WAAU;wCACV,eAAY;;;;;;kDAEd,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,cAAW;wCACX,IAAG;wCACH,MAAK;;;;;;;;;;;;0CAGT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmD;;;;;;0DACnE,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAY,eAAe;;kEACxC,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAe;;;;;;0EACjC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAc;;;;;;0EAChC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;;;;;;;;;;;;;;;;;;;kDAKpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmD;;;;;;0DACnE,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,QAAQ;gDACxB,eAAe,CAAC;oDACd,YAAY,OAAO;oDACnB,eAAe,IAAI,8CAA8C;gDACnE;;kEAEA,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQhC,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAK,WAAU;0CAA4B;;;;;;;;;;;+BAE5C,sBACF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;0CACxD,8OAAC;gCAAE,WAAU;0CAAkC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wCACrC,WAAU;kDACX;;;;;;oCAGA,MAAM,QAAQ,CAAC,kCACd,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAO,OAAO,QAAQ,CAAC,IAAI,GAAG;wCACvC,WAAU;kDACX;;;;;;;;;;;;;;;;;+BAML,gBAAgB,MAAM,KAAK,kBAC7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;0CACxD,8OAAC;gCAAE,WAAU;0CACV,aACG,6CACA;;;;;;0CAEN,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,UAAU;4CACV,WAAU;sDAET,mCACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;kEAEjC,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAK/B,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAS,WAAU;;0DAC5C,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,8OAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAMd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC;oCACpB,qDAAqD;oCACrD,MAAM,eAAe,gBAAgB;oCACrC,MAAM,MAAM,aAAa,EAAE,IAAI,KAAK,MAAM,GAAG,QAAQ;oCAErD,qBACE,8OAAC,sKAAA,CAAA,UAAY;wCAAW,UAAU;wCAAU,SAAS;uCAAlC;;;;;gCAEvB;;;;;;4BAID,aAAa,mBACZ,8OAAC,sIAAA,CAAA,aAAU;gCAAC,WAAU;gCAAO,cAAW;0CACtC,cAAA,8OAAC,sIAAA,CAAA,oBAAiB;;sDAChB,8OAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;gDACjB,MAAK;gDACL,SAAS,IAAM,eAAe,CAAC,OAAS,KAAK,GAAG,CAAC,OAAO,GAAG;gDAC3D,WAAW,GAAG,eAAe,IAAI,mCAAmC,GAAG,0FAA0F,CAAC;gDAClK,iBAAe,eAAe;;;;;;;;;;;wCAKjC,cAAc,mBACb,8OAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DACX;;;;;;;;;;;wCAOJ,cAAc,mBACb,8OAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;gDAAC,WAAU;;;;;;;;;;;wCAKjC,cAAc,mBACb,8OAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,SAAS,IAAM,eAAe,cAAc;gDAC5C,WAAU;0DAET,cAAc;;;;;;;;;;;sDAMrB,8OAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,QAAQ;gDACR,WAAU;0DAET;;;;;;;;;;;wCAKJ,cAAc,4BACb,8OAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,SAAS,IAAM,eAAe,cAAc;gDAC5C,WAAU;0DAET,cAAc;;;;;;;;;;;wCAMpB,cAAc,aAAa,mBAC1B,8OAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;gDAAC,WAAU;;;;;;;;;;;wCAKjC,cAAc,aAAa,mBAC1B,8OAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DAET;;;;;;;;;;;sDAKP,8OAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,SAAS,IAAM,eAAe,CAAC,OAAS,KAAK,GAAG,CAAC,OAAO,GAAG;gDAC3D,WAAW,GAAG,eAAe,aAAa,mCAAmC,GAAG,0FAA0F,CAAC;gDAC3K,iBAAe,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpD", "debugId": null}}]}