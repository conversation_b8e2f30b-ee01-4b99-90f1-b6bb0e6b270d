(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_9e4fce59._.js", {

"[project]/src/components/providers/theme-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
"use client";
;
;
function ThemeProvider({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/theme-provider.tsx",
        lineNumber: 8,
        columnNumber: 10
    }, this);
}
_c = ThemeProvider;
var _c;
__turbopack_context__.k.register(_c, "ThemeProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/userStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * User store for authentication state management
 * Matches the implementation in ruh-app-fe for consistency.
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "useUserStore": (()=>useUserStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
const useUserStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        // Actions
        setUser: (userData)=>set((state)=>({
                    // Merge existing user data with new data if user exists
                    user: state.user ? {
                        ...state.user,
                        ...userData
                    } : userData,
                    // Update isAuthenticated based on presence of accessToken
                    isAuthenticated: userData?.accessToken ? true : false
                })),
        clearUser: ()=>set({
                user: null,
                isAuthenticated: false
            }),
        // Logout action that calls the authApi logout method
        logout: async ()=>{
            try {
                // Import dynamically to avoid circular dependencies
                const { authApi } = await __turbopack_context__.r("[project]/src/lib/authApi.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                await authApi.logout();
            } catch (error) {
                console.error("Error during logout:", error);
                // Clear user state even if logout API fails
                get().clearUser();
            }
        }
    }), {
    name: "user-storage"
}));
const __TURBOPACK__default__export__ = useUserStore;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/authCookies.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/utils/authCookies.ts
__turbopack_context__.s({
    "ACCESS_TOKEN_NAME": (()=>ACCESS_TOKEN_NAME),
    "REFRESH_TOKEN_NAME": (()=>REFRESH_TOKEN_NAME),
    "clearAuthCookies": (()=>clearAuthCookies),
    "getAccessToken": (()=>getAccessToken),
    "getRefreshToken": (()=>getRefreshToken),
    "setAccessToken": (()=>setAccessToken),
    "setRefreshToken": (()=>setRefreshToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cookies-next/lib/index.js [app-client] (ecmascript)");
;
const ACCESS_TOKEN_NAME = "access_token";
const REFRESH_TOKEN_NAME = "refresh_token";
const getAccessToken = async ()=>{
    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCookie"])(ACCESS_TOKEN_NAME);
    return token ? String(token) : null;
};
const getRefreshToken = async ()=>{
    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCookie"])(REFRESH_TOKEN_NAME);
    return token ? String(token) : null;
};
const setAccessToken = (token, expiresIn = 3600)=>{
    const expirationDate = new Date();
    expirationDate.setSeconds(expirationDate.getSeconds() + expiresIn);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookie"])(ACCESS_TOKEN_NAME, token, {
        expires: expirationDate,
        path: "/",
        secure: ("TURBOPACK compile-time value", "development") === "production",
        sameSite: "strict"
    });
};
const setRefreshToken = (token, expiresIn = 30 * 24 * 60 * 60)=>{
    const expirationDate = new Date();
    expirationDate.setSeconds(expirationDate.getSeconds() + expiresIn);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookie"])(REFRESH_TOKEN_NAME, token, {
        expires: expirationDate,
        path: "/",
        secure: ("TURBOPACK compile-time value", "development") === "production",
        sameSite: "strict"
    });
};
const clearAuthCookies = async ()=>{
    // Clear access token with default path
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteCookie"])(ACCESS_TOKEN_NAME, {
        path: "/"
    });
    // Clear refresh token with both possible paths to ensure it's removed
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteCookie"])(REFRESH_TOKEN_NAME, {
        path: "/"
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteCookie"])(REFRESH_TOKEN_NAME, {
        path: "/api/auth/refresh"
    });
    console.log("Auth cookies cleared with specific paths");
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/clientCookies.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Client-side cookie utilities
 * These functions can be used in client components to get/set cookies in the browser
 */ __turbopack_context__.s({
    "checkClientAccessToken": (()=>checkClientAccessToken),
    "clearClientAuthCookies": (()=>clearClientAuthCookies),
    "getClientAccessToken": (()=>getClientAccessToken),
    "getClientRefreshToken": (()=>getClientRefreshToken),
    "setClientAuthCookies": (()=>setClientAuthCookies)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
;
const getClientAccessToken = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("accessToken") || "";
};
const getClientRefreshToken = ()=>{
    // The refresh token is now HTTP-only and not accessible from client-side JavaScript
    // This function is kept for backward compatibility but will always return null
    return null;
};
const checkClientAccessToken = ()=>{
    const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("accessToken");
    console.log("Client-side access token check:", !!token);
    return !!token;
};
const setClientAuthCookies = (accessToken, refreshToken, accessTokenAge, refreshTokenAge)=>{
    // Set access token cookie
    // Note: The refresh token is handled by the server-side HTTP-only cookie
    // and should not be set on the client side
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set("accessToken", accessToken, {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost"),
        secure: true,
        sameSite: "lax",
        expires: accessTokenAge / (60 * 60 * 24)
    });
// We no longer set the refresh token on the client side
// as it's now handled by an HTTP-only cookie set by the server
// This improves security by preventing client-side JavaScript from accessing the refresh token
};
const clearClientAuthCookies = ()=>{
    // Clear access token from client-side cookie
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove("accessToken", {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost")
    });
    // Attempt to clear refresh token with both possible paths
    // Even though it's HTTP-only, we try to clear it from client-side as a fallback
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove("refreshToken", {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost")
    });
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove("refreshToken", {
        path: "/api/auth/refresh",
        domain: ("TURBOPACK compile-time value", "localhost")
    });
    console.log("Client-side cookie clearing attempted for both tokens");
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/shared/routes.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Shared route definitions for the application
 * Matches the implementation in ruh-app-fe for consistency.
 */ // Authentication routes
__turbopack_context__.s({
    "aboutRoute": (()=>aboutRoute),
    "authRoute": (()=>authRoute),
    "contactRoute": (()=>contactRoute),
    "credentialsRoute": (()=>credentialsRoute),
    "homeRoute": (()=>homeRoute),
    "loginRoute": (()=>loginRoute),
    "protectedRoutes": (()=>protectedRoutes),
    "publicRoutes": (()=>publicRoutes),
    "settingsRoute": (()=>settingsRoute),
    "signupRoute": (()=>signupRoute),
    "updatePasswordRoute": (()=>updatePasswordRoute),
    "verifyEmailRoute": (()=>verifyEmailRoute),
    "workflowsRoute": (()=>workflowsRoute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const loginRoute = "/login";
const signupRoute = "/signup";
const verifyEmailRoute = "/verify-email";
const updatePasswordRoute = "/reset-password";
const authRoute = `${("TURBOPACK compile-time value", "http://localhost:3001/")}?redirect_url=${("TURBOPACK compile-time value", "http://localhost:3000/")}`;
const homeRoute = "/workflows"; // Updated to point to workflows instead of /home
const workflowsRoute = "/workflows";
const settingsRoute = "/settings";
const credentialsRoute = "/credentials";
const aboutRoute = "/about";
const contactRoute = "/contact";
const publicRoutes = [
    loginRoute,
    signupRoute,
    verifyEmailRoute,
    updatePasswordRoute,
    aboutRoute,
    contactRoute
];
const protectedRoutes = [
    workflowsRoute,
    workflowsRoute,
    settingsRoute,
    credentialsRoute
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/use-user.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/hooks/use-user.ts
__turbopack_context__.s({
    "useUserStore": (()=>useUserStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
const useUserStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        user: null,
        setUser: (user)=>set({
                user: {
                    ...user,
                    isAuthenticated: true
                }
            }),
        clearUser: ()=>set({
                user: null
            }),
        isAuthenticated: ()=>!!get().user?.isAuthenticated
    }), {
    name: "user-storage"
}));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/axios.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "authApi": (()=>authApi),
    "createAxiosInstance": (()=>createAxiosInstance),
    "default": (()=>__TURBOPACK__default__export__),
    "externalApi": (()=>externalApi),
    "logout": (()=>logout),
    "workflowApi": (()=>workflowApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/authCookies.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/clientCookies.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/routes.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$user$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-user.ts [app-client] (ecmascript)");
;
;
;
;
;
// Helper function to clear user store
const clearUserStore = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$user$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser();
};
// Helper function to get appropriate token based on environment
const getAppropriateToken = async (enableClientSide = false)=>{
    if (enableClientSide && "object" !== "undefined") {
        // Client-side
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getClientAccessToken"])();
    } else {
        // Server-side or fallback
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAccessToken"])();
    }
};
// Create request interceptor
const createRequestInterceptor = (config = {})=>{
    return async (requestConfig)=>{
        // Ensure headers object exists
        if (!requestConfig.headers) {
            requestConfig.headers = {};
        }
        const token = await getAppropriateToken(config.enableClientSideToken);
        if (token) {
            requestConfig.headers.Authorization = `Bearer ${token}`;
            console.log(`[DEBUG] Added Authorization header with token (length: ${token.length})`);
        } else {
            console.log(`[DEBUG] No token available for request to ${requestConfig.url}`);
        }
        // Add common headers
        requestConfig.headers["ngrok-skip-browser-warning"] = "true";
        // Add custom headers if provided
        if (config.customHeaders) {
            Object.assign(requestConfig.headers, config.customHeaders);
        }
        return requestConfig;
    };
};
// Create response interceptor
const createResponseInterceptor = (instance, config = {})=>{
    return async (error)=>{
        const originalRequest = error.config;
        // Only handle token refresh if enabled
        if (!config.enableTokenRefresh) {
            return Promise.reject(error);
        }
        // Check if the error is due to an expired token (401 Unauthorized or 403 Forbidden)
        if ((error.response?.status === 401 || error.response?.status === 403) && !originalRequest._retry) {
            // Mark this request as retried to prevent infinite loops
            originalRequest._retry = true;
            try {
                // Call the refresh token endpoint using a fresh axios instance to avoid infinite loops
                // This endpoint will use the HTTP-only refresh token cookie automatically
                const refreshInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create();
                const response = await refreshInstance.post("/api/auth/refresh");
                if (response.data.success && response.data.accessToken) {
                    // Update the authorization header with the new token
                    originalRequest.headers = {
                        ...originalRequest.headers,
                        Authorization: `Bearer ${response.data.accessToken}`
                    };
                    // Retry the original request with the new token
                    return instance(originalRequest);
                } else {
                    // Token refresh failed
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
                    clearUserStore();
                    if ("TURBOPACK compile-time truthy", 1) {
                        window.location.href = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loginRoute"];
                    }
                    return Promise.reject(new Error("Token refresh failed"));
                }
            } catch (refreshError) {
                // Clear cookies and redirect to login on refresh error
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
                clearUserStore();
                if ("TURBOPACK compile-time truthy", 1) {
                    window.location.href = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loginRoute"];
                }
                return Promise.reject(refreshError);
            }
        }
        // For other errors, just reject the promise
        return Promise.reject(error);
    };
};
const createAxiosInstance = (config = {})=>{
    const instance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
        baseURL: config.baseURL || ("TURBOPACK compile-time value", "http://localhost:8000/api/v1"),
        withCredentials: config.withCredentials ?? false
    });
    // Add request interceptor
    instance.interceptors.request.use(createRequestInterceptor(config), {
        "createAxiosInstance.use": (error)=>{
            return Promise.reject(new Error(`Request interceptor error: ${error.message || "Unknown error"}`));
        }
    }["createAxiosInstance.use"]);
    // Add response interceptor
    instance.interceptors.response.use({
        "createAxiosInstance.use": (response)=>response
    }["createAxiosInstance.use"], createResponseInterceptor(instance, config));
    return instance;
};
// Main API instance with full token refresh capabilities
const api = createAxiosInstance({
    enableTokenRefresh: true,
    enableClientSideToken: true
});
const workflowApi = createAxiosInstance({
    enableTokenRefresh: false,
    enableClientSideToken: true
});
const authApi = createAxiosInstance({
    enableTokenRefresh: true,
    enableClientSideToken: true,
    withCredentials: true
});
const externalApi = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create();
const logout = async ()=>{
    try {
        // Clear all auth cookies (both access and refresh tokens)
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
        // Clear user store
        clearUserStore();
        // Redirect to login page
        if ("TURBOPACK compile-time truthy", 1) {
            window.location.href = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loginRoute"];
        }
    } catch (error) {
        console.error("Error during logout:", error);
        // Even if there's an error, try to clear everything
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
        clearUserStore();
        if ("TURBOPACK compile-time truthy", 1) {
            window.location.href = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loginRoute"];
        }
    }
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/cookies.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ {"7f06e5dfd84ab0c07ed31b04d4f37ae17ace602a2c":"setRefreshingTokenCookie","7f6afb28085f3f5ab4bce42ecd8376b99d1e52fa86":"getRefreshToken","7f7585f7cb94f28ad986a1c31916a1a410528e6cd1":"getAccessToken","7f7eeb76341200e95f0cfaf6a1775b37ae31bcda34":"clearAuthCookies","7fabf9d42b3dd78768353f5b837f3883332691548e":"setAuthCookies","7fc5568e0d82b85c58be597d55e74cee163135f728":"checkAccessToken","7fff563ba7c3f776eb4d4439783e3dcdde31d52169":"clearRefreshingTokenCookie"} */ __turbopack_context__.s({
    "checkAccessToken": (()=>checkAccessToken),
    "clearAuthCookies": (()=>clearAuthCookies),
    "clearRefreshingTokenCookie": (()=>clearRefreshingTokenCookie),
    "getAccessToken": (()=>getAccessToken),
    "getRefreshToken": (()=>getRefreshToken),
    "setAuthCookies": (()=>setAuthCookies),
    "setRefreshingTokenCookie": (()=>setRefreshingTokenCookie)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
;
var getAccessToken = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f7585f7cb94f28ad986a1c31916a1a410528e6cd1", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "getAccessToken");
var getRefreshToken = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f6afb28085f3f5ab4bce42ecd8376b99d1e52fa86", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "getRefreshToken");
var checkAccessToken = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7fc5568e0d82b85c58be597d55e74cee163135f728", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "checkAccessToken");
var setAuthCookies = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7fabf9d42b3dd78768353f5b837f3883332691548e", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "setAuthCookies");
var clearAuthCookies = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f7eeb76341200e95f0cfaf6a1775b37ae31bcda34", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "clearAuthCookies");
var setRefreshingTokenCookie = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f06e5dfd84ab0c07ed31b04d4f37ae17ace602a2c", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "setRefreshingTokenCookie");
var clearRefreshingTokenCookie = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7fff563ba7c3f776eb4d4439783e3dcdde31d52169", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "clearRefreshingTokenCookie");
}}),
"[project]/src/lib/apiConfig.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * API Configuration
 *
 * This file centralizes all API URL configurations to ensure consistency
 * across the application.
 *
 * Note: The API_BASE_URL already includes '/api/v1' from the environment variable
 * (e.g., https://app-dev.rapidinnovation.dev/api/v1)
 */ // Base API URL from environment variable (already includes /api/v1)
__turbopack_context__.s({
    "API_BASE_URL": (()=>API_BASE_URL),
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "AUTH_API_URL": (()=>AUTH_API_URL),
    "MCPS_API_URL": (()=>MCPS_API_URL),
    "WORKFLOWS_API_URL": (()=>WORKFLOWS_API_URL),
    "WORKFLOW_EXECUTION_URL": (()=>WORKFLOW_EXECUTION_URL),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:8000/api/v1");
const AUTH_API_URL = `${API_BASE_URL}/auth`;
const WORKFLOWS_API_URL = `${API_BASE_URL}/workflows`;
const MCPS_API_URL = `${API_BASE_URL}/mcps`;
const WORKFLOW_EXECUTION_URL = ("TURBOPACK compile-time value", "https://ruh-test-api.rapidinnovation.dev/api/v1");
const API_ENDPOINTS = {
    // Auth endpoints
    AUTH: {
        LOGIN: `${AUTH_API_URL}/login`,
        REGISTER: `${AUTH_API_URL}/register`,
        LOGOUT: `${AUTH_API_URL}/logout`,
        REFRESH: `${AUTH_API_URL}/refresh`,
        FORGOT_PASSWORD: `${AUTH_API_URL}/forgot-password`,
        RESET_PASSWORD: `${AUTH_API_URL}/reset-password`,
        VERIFY_EMAIL: `${AUTH_API_URL}/verify-email`,
        VERIFY_EMAIL_OTP: `${AUTH_API_URL}/verify-email-otp`,
        UPDATE_PASSWORD: `${AUTH_API_URL}/update-password`
    },
    // Workflow endpoints
    WORKFLOWS: {
        LIST: `${WORKFLOWS_API_URL}`,
        CREATE: `${WORKFLOWS_API_URL}`,
        GET: (id)=>`${WORKFLOWS_API_URL}/${id}`,
        UPDATE: (id)=>`${WORKFLOWS_API_URL}/${id}`,
        DELETE: (id)=>`${WORKFLOWS_API_URL}/${id}`,
        EXECUTE: (id)=>`${WORKFLOWS_API_URL}/${id}/execute`
    },
    // MCP endpoints
    MCPS: {
        LIST: `${MCPS_API_URL}`,
        GET: (id)=>`${MCPS_API_URL}/${id}`
    },
    // Workflow Execution endpoints (separate service)
    WORKFLOW_EXECUTION: {
        EXECUTE: `${WORKFLOW_EXECUTION_URL}/workflow-execute/execute`,
        APPROVE: `${WORKFLOW_EXECUTION_URL}/workflow-execute/approve`,
        STREAM: `${WORKFLOW_EXECUTION_URL}/workflow-execute/stream`
    }
};
const __TURBOPACK__default__export__ = {
    API_BASE_URL,
    AUTH_API_URL,
    WORKFLOWS_API_URL,
    MCPS_API_URL,
    WORKFLOW_EXECUTION_URL,
    API_ENDPOINTS
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/authApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Authentication API client for workflow-builder-poc
 * This module provides functions for authentication operations.
 * Matches the implementation in ruh-app-fe for consistency.
 */ __turbopack_context__.s({
    "authApi": (()=>authApi),
    "default": (()=>__TURBOPACK__default__export__),
    "hasCompletedOnboarding": (()=>hasCompletedOnboarding)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/axios.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cookies.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/clientCookies.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/userStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiConfig.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/routes.ts [app-client] (ecmascript)");
;
;
;
;
;
;
async function hasCompletedOnboarding() {
    try {
        const userDetails = await authApi.getCurrentUser();
        return Boolean(userDetails?.department && userDetails?.jobRole);
    } catch (error) {
        // Check if this request has already been through the axios interceptor
        const originalRequest = error.config;
        if (originalRequest && originalRequest._retry) {
            // This request has already been through the interceptor and still failed
            // This means token refresh failed, so we should redirect to login
            throw error;
        }
        // For other errors, return false to allow normal flow
        return false;
    }
}
const authApi = {
    /**
   * Login user with email and password
   * @param data LoginType from zod schema
   */ login: async (data)=>{
        const { email, password } = data;
        try {
            // Prepare login payload
            const payload = {
                email,
                password
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.LOGIN, payload);
            if (!response.data.access_token) {
                throw new Error("Login failed: Unexpected response from server.");
            }
            // Set auth cookies after successful login (both server-side and client-side)
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAuthCookies"])(response.data.access_token, response.data.refresh_token, response.data.accessTokenAge || 36000, response.data.refreshTokenAge || 86400);
            // Also set client-side cookies for immediate access
            if ("TURBOPACK compile-time truthy", 1) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setClientAuthCookies"])(response.data.access_token, response.data.refresh_token, response.data.accessTokenAge || 36000, response.data.refreshTokenAge || 86400);
                console.log("Client-side auth cookies set after login");
            }
            let redirectPath = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["workflowsRoute"]; // Use workflows route directly
            try {
                // Fetch user details after successful login
                const userDetails = await authApi.getCurrentUser();
                // Update the user store with user data and access token
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"].getState().setUser({
                    fullName: userDetails.fullName,
                    email: userDetails.email,
                    accessToken: response.data.access_token
                });
                // Keep using workflows route
                redirectPath = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["workflowsRoute"];
            } catch (userError) {
                console.error("Failed to fetch user details:", userError);
            // Keep redirectPath as '/workflows' if fetching user details fails
            }
            // Return both login data and the determined redirect path
            return {
                loginData: response.data,
                redirectPath
            };
        } catch (error) {
            // Clear user store on login failure
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser();
            if (error.response?.status === 404) {
                throw new Error("User not found.");
            }
            if (error.response?.status === 412) {
                throw new Error("Account inactive. Please check your email for verification.");
            }
            throw new Error(error.response?.data?.detail || error.response?.data?.message || "Invalid Credentials");
        }
    },
    /**
   * Register new user
   * @param data SignupType from zod schema
   */ signup: async (data)=>{
        try {
            const { email, fullName, password } = data;
            const payload = {
                full_name: fullName,
                email,
                password
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.REGISTER, payload);
            return response.data;
        } catch (error) {
            if (error.response?.status === 409) {
                throw new Error(error.response?.data?.detail || "Email already registered.");
            }
            throw new Error(error.response?.data?.detail || error.response?.data?.message || "Signup failed");
        }
    },
    /**
   * Logout user
   */ logout: async ()=>{
        try {
            // Clear server-side auth cookies
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
            // Clear client-side auth cookies
            if ("TURBOPACK compile-time truthy", 1) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearClientAuthCookies"])();
                console.log("Client-side auth cookies cleared during logout");
            }
            // Clear user store (including accessToken)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser();
            console.log("User store cleared during logout");
            // Redirect to login page
            if ("TURBOPACK compile-time truthy", 1) {
                window.location.href = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loginRoute"];
            }
        } catch (error) {
            console.error("Error during logout:", error);
            // Even if there's an error, try to clear everything
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
            if ("TURBOPACK compile-time truthy", 1) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearClientAuthCookies"])();
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser();
            if ("TURBOPACK compile-time truthy", 1) {
                window.location.href = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loginRoute"];
            }
        }
    },
    /**
   * Forgot password
   * @param email Email address for password reset
   */ forgotPassword: async (email)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.FORGOT_PASSWORD, null, {
                params: {
                    email
                }
            });
            return response.data; // Return the success message
        } catch (error) {
            throw new Error(error.response?.data?.detail || // Use detail field if available
            error.response?.data?.message || "Failed to send password reset email");
        }
    },
    /**
   * Reset user's password using OTP token
   * @param token The OTP token from the reset link
   * @param data ResetPasswordType containing newPassword and confirmNewPassword
   */ resetPassword: async (token, data)=>{
        const { newPassword, confirmNewPassword } = data;
        try {
            // Construct the payload for the API
            const payload = {
                token,
                new_password: newPassword,
                confirm_new_password: confirmNewPassword
            };
            // Call the actual API endpoint
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.UPDATE_PASSWORD, payload);
            return response.data; // Return the success message from the API
        } catch (error) {
            throw new Error(// Use error details provided by the API response preferentially
            error.response?.data?.detail || error.response?.data?.message || "Password reset failed");
        }
    },
    /**
   * Update user's password
   * @param data Object containing token and password
   */ updatePassword: async (data)=>{
        try {
            // Construct the payload for the API
            const payload = {
                token: data.token,
                new_password: data.password,
                confirm_new_password: data.password
            };
            // Call the actual API endpoint
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.UPDATE_PASSWORD, payload);
            return response.data; // Return the success message from the API
        } catch (error) {
            throw new Error(// Use error details provided by the API response preferentially
            error.response?.data?.detail || error.response?.data?.message || "Password update failed");
        }
    },
    /**
   * Verify email using OTP token
   * @param token The OTP token from the verification link
   */ verifyEmailOtp: async (token)=>{
        try {
            const payload = {
                token
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.VERIFY_EMAIL_OTP, payload);
            return response.data;
        } catch (error) {
            throw new Error(error.response?.data?.detail || error.response?.data?.message || "Email verification failed");
        }
    },
    /**
   * Get current user information
   */ getCurrentUser: async ()=>{
        try {
            console.log("Making request to /users/me");
            // The centralized auth API will automatically handle authorization headers
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].get(`/users/me`);
            console.log("Successfully retrieved user data");
            return response.data;
        } catch (error) {
            console.error("Get current user error:", error);
            // Check if this is a 403 Forbidden error
            if (error.response?.status === 403) {
                console.log("Authentication error: 403 Forbidden. Token may be invalid or expired.");
                // Clear user state and cookies to force re-login
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser();
                if ("TURBOPACK compile-time truthy", 1) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearClientAuthCookies"])();
                }
            }
            throw new Error(error.response?.data?.detail || error.response?.data?.message || "Failed to fetch user details");
        }
    },
    /**
   * Verify current auth session by attempting to fetch user data.
   */ isLoggedIn: async ()=>{
        try {
            await authApi.getCurrentUser(); // Attempt to fetch user data
            return true; // If successful, user is considered logged in
        } catch (error) {
            // Any error (including 401/403 from getCurrentUser) means session is not valid
            return false;
        }
    },
    /**
   * Check if user is authenticated based on cookie presence
   * Uses client-side cookie access when in browser environment
   */ isAuthenticated: async ()=>{
        // Check if we're in a browser environment
        if ("TURBOPACK compile-time truthy", 1) {
            // Use client-side cookie access
            const isAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["checkClientAccessToken"])();
            console.log("Client-side isAuthenticated check:", isAuth);
            return isAuth;
        } else {
            "TURBOPACK unreachable";
        }
    },
    /**
   * Get the access token from cookies
   * Uses client-side cookie access when in browser environment
   */ getAccessToken: async ()=>{
        // Check if we're in a browser environment
        if ("TURBOPACK compile-time truthy", 1) {
            // Use client-side cookie access
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getClientAccessToken"])();
        } else {
            "TURBOPACK unreachable";
        }
    },
    /**
   * Generate new access token using refresh token
   * @param refreshToken The refresh token to use for generating a new access token
   */ generateAccessToken: async (refreshToken)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].post(`/auth/access-token`, {}, {
                params: {
                    refresh_token: refreshToken
                }
            });
            // Update the access token in cookies if successful
            if (response.data.success && response.data.access_token) {
                // Calculate token age in seconds from tokenExpireAt
                const expireAt = new Date(response.data.tokenExpireAt).getTime();
                const now = new Date().getTime();
                const accessTokenAge = Math.floor((expireAt - now) / 1000);
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAuthCookies"])(response.data.access_token, null, accessTokenAge, null);
                // Also update the access token in the user store
                const currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"].getState().user;
                if (currentUser) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"].getState().setUser({
                        ...currentUser,
                        accessToken: response.data.access_token
                    });
                }
            }
            return response.data;
        } catch (error) {
            throw new Error(error.response?.data?.detail || error.response?.data?.message || "Failed to generate new access token");
        }
    },
    /**
   * Initiates the Google OAuth login flow by redirecting the browser.
   */ googleLogin: async ()=>{
        try {
            // Construct the full URL to the backend's Google login initiation endpoint.
            let googleLoginUrl = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_BASE_URL"]}/auth/google-login`;
            // Redirect the user's browser.
            window.location.href = googleLoginUrl;
        } catch (error) {
            console.error("Error during Google login:", error);
            // Fallback to basic Google login without FCM token
            window.location.href = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_BASE_URL"]}/auth/google-login`;
        }
    },
    /**
   * Handles the final steps after Google OAuth callback.
   * Assumes the backend set auth cookies before redirecting back to the frontend.
   */ finalizeGoogleLogin: async ()=>{
        try {
            // Fetch user details using the cookies potentially set by the backend callback
            const userDetails = await authApi.getCurrentUser();
            // Get the access token from cookies
            const accessToken = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAccessToken"])();
            // Update global user store with user details and access token
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"].getState().setUser({
                fullName: userDetails.fullName,
                email: userDetails.email,
                accessToken: accessToken
            });
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser(); // Reset state on error
            console.error("Failed to retrieve user details after Google login:", error);
        }
    }
};
const __TURBOPACK__default__export__ = authApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/auth-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/userStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/routes.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
function AuthProvider({ children }) {
    _s();
    const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"])({
        "AuthProvider.useUserStore[user]": (state)=>state.user
    }["AuthProvider.useUserStore[user]"]);
    const setUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"])({
        "AuthProvider.useUserStore[setUser]": (state)=>state.setUser
    }["AuthProvider.useUserStore[setUser]"]);
    const clearUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"])({
        "AuthProvider.useUserStore[clearUser]": (state)=>state.clearUser
    }["AuthProvider.useUserStore[clearUser]"]);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    // Check if the current path is public
    const isPublicRoute = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["publicRoutes"].some((route)=>// For exact matches (like login, signup)
        pathname === route || route.startsWith("/") && pathname.startsWith(route));
    // Check if user is authenticated
    const isAuthenticated = !!user?.accessToken;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            // Try to get current user on initial load
            const loadUser = {
                "AuthProvider.useEffect.loadUser": async ()=>{
                    try {
                        // Check if we have an access token in cookies
                        const isAuth = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].isAuthenticated();
                        console.log("Authentication check result:", isAuth);
                        if (isAuth) {
                            try {
                                // Try to get user data
                                const userData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].getCurrentUser();
                                console.log("User data retrieved successfully:", userData);
                                // Get the access token from cookies
                                const accessToken = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].getAccessToken();
                                // Update the user store
                                setUser({
                                    ...userData,
                                    accessToken
                                });
                            } catch (userError) {
                                // Handle specific authentication errors
                                if (userError.response?.status === 403 || userError.response?.status === 401) {
                                    console.log("Authentication error:", userError.response?.status);
                                    clearUser();
                                    // If we're not on a public route, redirect to login
                                    if (!isPublicRoute) {
                                        router.push("/login");
                                    }
                                } else {
                                    // For other errors, just log them but don't clear user yet
                                    console.error("Error fetching user data:", userError);
                                }
                            }
                        } else {
                            // No authentication token found
                            console.log("No authentication token found");
                            clearUser();
                        }
                    } catch (error) {
                        // If there's an error in the overall process, clear the user state
                        clearUser();
                        console.error("Failed to load user:", error);
                    }
                }
            }["AuthProvider.useEffect.loadUser"];
            loadUser();
        }
    }["AuthProvider.useEffect"], [
        setUser,
        clearUser,
        router,
        isPublicRoute
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            // Handle route protection
            if (!isPublicRoute && !isAuthenticated) {
                router.push(`/login`);
            }
            // Redirect authenticated users away from auth pages
            if (isPublicRoute && isAuthenticated) {
                router.push("/");
            }
        }
    }["AuthProvider.useEffect"], [
        pathname,
        isAuthenticated,
        isPublicRoute,
        router
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(AuthProvider, "NnVdnRFqARFHFzRMvDk3urQEVM4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/QueryProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "QueryProvider": (()=>QueryProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
function QueryProvider({ children }) {
    _s();
    // Create a client for each session to avoid shared state between users
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "QueryProvider.useState": ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]()
    }["QueryProvider.useState"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/QueryProvider.tsx",
        lineNumber: 14,
        columnNumber: 10
    }, this);
}
_s(QueryProvider, "qFhNRSk+4hqflxYLL9+zYF5AcuQ=");
_c = QueryProvider;
var _c;
__turbopack_context__.k.register(_c, "QueryProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/ToastProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ToastProvider": (()=>ToastProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
function ToastProvider() {
    _s();
    const { theme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {
        position: "top-right",
        toastOptions: {
            style: {
                background: theme === "dark" ? "#222222" : "#F9F7F7",
                color: theme === "dark" ? "#f0f0f0" : "#112D4E",
                border: theme === "dark" ? "1px solid rgba(29, 205, 159, 0.1)" : "1px solid rgba(63, 114, 175, 0.1)"
            }
        }
    }, void 0, false, {
        fileName: "[project]/src/providers/ToastProvider.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
}
_s(ToastProvider, "JkSxfi8+JQlqgIgDOc3wQN+nVIw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = ToastProvider;
var _c;
__turbopack_context__.k.register(_c, "ToastProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_9e4fce59._.js.map