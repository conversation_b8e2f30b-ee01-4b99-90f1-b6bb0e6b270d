{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "eccd3a76bd4497472f1f2ff62603e362", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e91c0a3879b9dddf8138e4a272b59d685f349a64c7f80e88ec8bf1eed6d29dc8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2fbcedbb00870c6c7776f1d47e0694f9b4a1a7b87ed6ed81b256e658d3f82783"}}}, "sortedMiddleware": ["/"], "functions": {}}