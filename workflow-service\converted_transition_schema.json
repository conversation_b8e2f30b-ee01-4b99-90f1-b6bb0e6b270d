{"nodes": [{"id": "ConditionalNode", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "ConditionalNode", "input_schema": {"predefined_fields": [{"field_name": "primary_input_data", "data_type": {"type": "string", "description": "Main data to route through conditions. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "condition_1_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_1_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_1_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_1_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_1_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_1_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_2_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_2_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_2_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_2_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_2_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_2_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "num_additional_conditions", "data_type": {"type": "number", "description": "Number of additional conditions beyond the base 2 conditions (0-8). Total conditions will be 2 + this value."}, "required": false}, {"field_name": "condition_3_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_3_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_3_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_3_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_3_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_3_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_4_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_4_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_4_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_4_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_4_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_4_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_5_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_5_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_5_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_5_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_5_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_5_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_6_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_6_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_6_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_6_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_6_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_6_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_7_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_7_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_7_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_7_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_7_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_7_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_8_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_8_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_8_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_8_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_8_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_8_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_9_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_9_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_9_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_9_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_9_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_9_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_10_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_10_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_10_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_10_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_10_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_10_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_1_input_handle", "data_type": {"type": "string", "description": "Input data for condition 1 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_2_input_handle", "data_type": {"type": "string", "description": "Input data for condition 2 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_3_input_handle", "data_type": {"type": "string", "description": "Input data for condition 3 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_4_input_handle", "data_type": {"type": "string", "description": "Input data for condition 4 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_5_input_handle", "data_type": {"type": "string", "description": "Input data for condition 5 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_6_input_handle", "data_type": {"type": "string", "description": "Input data for condition 6 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_7_input_handle", "data_type": {"type": "string", "description": "Input data for condition 7 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_8_input_handle", "data_type": {"type": "string", "description": "Input data for condition 8 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_9_input_handle", "data_type": {"type": "string", "description": "Input data for condition 9 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_10_input_handle", "data_type": {"type": "string", "description": "Input data for condition 10 evaluation (only when source is Node Output)"}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "default_output", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "AgenticAI", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AgenticAI", "input_schema": {"predefined_fields": [{"field_name": "model_provider", "data_type": {"type": "string", "description": "The AI model provider to use."}, "required": false}, {"field_name": "base_url", "data_type": {"type": "string", "description": "Base URL for the API (leave empty for default provider URL)."}, "required": false}, {"field_name": "api_key", "data_type": {"type": "string", "description": "API key for the model provider. Can be entered directly or referenced from secure storage."}, "required": false}, {"field_name": "model_name", "data_type": {"type": "string", "description": "Select the model to use."}, "required": false}, {"field_name": "temperature", "data_type": {"type": "number", "description": "Controls randomness: 0 is deterministic, higher values are more random."}, "required": false}, {"field_name": "objective", "data_type": {"type": "string", "description": "The task or objective for the agent to accomplish. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "input_variables", "data_type": {"type": "object", "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "tools", "data_type": {"type": "array", "description": "List of tools available to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "memory", "data_type": {"type": "string", "description": "Connect a memory object from another node."}, "required": false}, {"field_name": "agent_type", "data_type": {"type": "string", "description": "The type of agent to create."}, "required": false}, {"field_name": "stream", "data_type": {"type": "boolean", "description": "Enable streaming for real-time responses."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "final_answer", "data_type": {"type": "string", "description": ""}}, {"field_name": "intermediate_steps", "data_type": {"type": "string", "description": ""}}, {"field_name": "updated_memory", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "ApiRequestNode", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "ApiRequestNode", "input_schema": {"predefined_fields": [{"field_name": "url", "data_type": {"type": "string", "description": "The URL to make the request to."}, "required": true}, {"field_name": "method", "data_type": {"type": "string", "description": "The HTTP method to use for the request."}, "required": false}, {"field_name": "query_params", "data_type": {"type": "object", "description": "Key-value pairs to append to the URL query string (optional)."}, "required": false}, {"field_name": "headers", "data_type": {"type": "object", "description": "Key-value pairs for request headers (optional)."}, "required": false}, {"field_name": "body", "data_type": {"type": "object", "description": "Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized."}, "required": false}, {"field_name": "timeout", "data_type": {"type": "number", "description": "Maximum time to wait for a response."}, "required": false}, {"field_name": "follow_redirects", "data_type": {"type": "boolean", "description": "Automatically follow HTTP redirects (e.g., 301, 302)."}, "required": false}, {"field_name": "save_to_file", "data_type": {"type": "boolean", "description": "Save response body to a temporary file instead of returning content directly. 'Result' output will be the file path."}, "required": false}, {"field_name": "output_format", "data_type": {"type": "string", "description": "'auto': Try JSON, fallback to text. 'json': Force JSON parsing (error if invalid). 'text': Return as text. 'bytes': Return raw bytes. 'file_path': Always save to file (requires 'Save Response to File'). 'metadata_dict': Return dict with status, headers, body/path, error."}, "required": false}, {"field_name": "raise_on_error", "data_type": {"type": "boolean", "description": "Stop workflow execution if an HTTP error status (4xx, 5xx) is received."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "result", "data_type": {"type": "string", "description": ""}}, {"field_name": "status_code", "data_type": {"type": "string", "description": ""}}, {"field_name": "response_headers", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "AlterMetadataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AlterMetadataComponent", "input_schema": {"predefined_fields": [{"field_name": "input_metadata", "data_type": {"type": "object", "description": "The metadata dictionary to modify. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "updates", "data_type": {"type": "object", "description": "Dictionary of key-value pairs to update or add to the metadata. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "keys_to_remove", "data_type": {"type": "array", "description": "List of keys to remove from the metadata. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_metadata", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "CombineTextComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "CombineTextComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "string", "description": "The main text or list to combine. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional text inputs to show (1-10)."}, "required": false}, {"field_name": "separator", "data_type": {"type": "string", "description": "The character or string to join the text with."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "string", "description": "Text for input 1. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "string", "description": "Text for input 2. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "string", "description": "Text for input 3. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "string", "description": "Text for input 4. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "string", "description": "Text for input 5. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "string", "description": "Text for input 6. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "string", "description": "Text for input 7. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "string", "description": "Text for input 8. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "string", "description": "Text for input 9. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "string", "description": "Text for input 10. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "result", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}], "transitions": [{"id": "transition-CombineTextComponent-1748506284632", "sequence": 1, "transition_type": "initial", "execution_type": "Components", "node_info": {"node_id": "CombineTextComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "CombineTextComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "string", "field_value": null}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": null}, {"field_name": "separator", "data_type": "string", "field_value": null}, {"field_name": "input_1", "data_type": "string", "field_value": null}, {"field_name": "input_2", "data_type": "string", "field_value": null}, {"field_name": "input_3", "data_type": "string", "field_value": null}, {"field_name": "input_4", "data_type": "string", "field_value": null}, {"field_name": "input_5", "data_type": "string", "field_value": null}, {"field_name": "input_6", "data_type": "string", "field_value": null}, {"field_name": "input_7", "data_type": "string", "field_value": null}, {"field_name": "input_8", "data_type": "string", "field_value": null}, {"field_name": "input_9", "data_type": "string", "field_value": null}, {"field_name": "input_10", "data_type": "string", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-start-node", "source_node_id": "Start", "data_type": "string", "mapping": [{"from_field": "flow", "to_field": "main_input", "source_handle": "flow", "target_handle": "main_input", "edge_id": "reactflow__edge-start-nodeflow-CombineTextComponent-1748506284632main_input", "mapping_type": "direct", "confidence": "low"}]}], "output_data": [{"to_transition_id": "transition-ConditionalNode-1748495878571", "target_node_id": "Switch-Case Router", "data_type": "string", "output_metadata": {"source_handle": "result", "target_handle": "primary_input_data", "edge_id": "reactflow__edge-CombineTextComponent-1748506284632result-ConditionalNode-1748495878571primary_input_data", "expected_output_fields": ["result", "error"], "field_mappings": {"result": "result"}}}]}, "approval_required": false, "end": false}, {"id": "transition-ConditionalNode-1748495878571", "sequence": 2, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "ConditionalNode", "tools_to_use": [{"tool_id": 1, "tool_name": "ConditionalNode", "tool_params": {"items": [{"field_name": "primary_input_data", "data_type": "string", "field_value": "${primary_input_data}"}, {"field_name": "condition_1_source", "data_type": "string", "field_value": "node_output"}, {"field_name": "condition_1_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_1_operator", "data_type": "string", "field_value": "less_than"}, {"field_name": "condition_1_expected_value", "data_type": "string", "field_value": "Hello"}, {"field_name": "condition_1_use_primary", "data_type": "boolean", "field_value": true}, {"field_name": "condition_1_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_2_source", "data_type": "string", "field_value": "global_context"}, {"field_name": "condition_2_variable", "data_type": "string", "field_value": "marketing"}, {"field_name": "condition_2_operator", "data_type": "string", "field_value": "ends_with"}, {"field_name": "condition_2_expected_value", "data_type": "string", "field_value": "hello"}, {"field_name": "condition_2_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_2_custom_input", "data_type": "string", "field_value": null}, {"field_name": "num_additional_conditions", "data_type": "number", "field_value": null}, {"field_name": "condition_3_source", "data_type": "string", "field_value": null}, {"field_name": "condition_3_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_3_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_3_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_3_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_3_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_4_source", "data_type": "string", "field_value": null}, {"field_name": "condition_4_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_4_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_4_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_4_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_4_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_5_source", "data_type": "string", "field_value": null}, {"field_name": "condition_5_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_5_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_5_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_5_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_5_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_6_source", "data_type": "string", "field_value": null}, {"field_name": "condition_6_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_6_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_6_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_6_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_6_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_7_source", "data_type": "string", "field_value": null}, {"field_name": "condition_7_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_7_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_7_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_7_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_7_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_8_source", "data_type": "string", "field_value": null}, {"field_name": "condition_8_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_8_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_8_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_8_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_8_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_9_source", "data_type": "string", "field_value": null}, {"field_name": "condition_9_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_9_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_9_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_9_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_9_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_10_source", "data_type": "string", "field_value": null}, {"field_name": "condition_10_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_10_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_10_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_10_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_10_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_1_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_2_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_3_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_4_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_5_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_6_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_7_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_8_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_9_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_10_input_handle", "data_type": "string", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-CombineTextComponent-1748506284632", "source_node_id": "Combine Text", "data_type": "string", "mapping": [{"from_field": "result", "to_field": "primary_input_data", "source_handle": "result", "target_handle": "primary_input_data", "edge_id": "reactflow__edge-CombineTextComponent-1748506284632result-ConditionalNode-1748495878571primary_input_data", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": [{"to_transition_id": "transition-AgenticAI-1748495964798", "target_node_id": "AI Agent Executor", "data_type": "string", "output_metadata": {"source_handle": "condition_1_output", "target_handle": "objective", "edge_id": "reactflow__edge-ConditionalNode-1748495878571condition_1_output-AgenticAI-1748495964798objective", "expected_output_fields": ["default_output"], "field_mappings": {"condition_1_output": "condition_1_output"}}}, {"to_transition_id": "transition-ApiRequestNode-1748495979157", "target_node_id": "API Request", "data_type": "string", "output_metadata": {"source_handle": "condition_2_output", "target_handle": "query_params", "edge_id": "reactflow__edge-ConditionalNode-1748495878571condition_2_output-ApiRequestNode-1748495979157query_params", "expected_output_fields": ["default_output"], "field_mappings": {"condition_2_output": "condition_2_output"}}}, {"to_transition_id": "transition-AlterMetadataComponent-1748495993158", "target_node_id": "<PERSON>er Metada<PERSON>", "data_type": "string", "output_metadata": {"source_handle": "default_output", "target_handle": "input_metadata", "edge_id": "reactflow__edge-ConditionalNode-1748495878571default_output-AlterMetadataComponent-1748495993158input_metadata", "expected_output_fields": ["default_output"], "field_mappings": {"default_output": "default_output"}}}]}, "approval_required": false, "end": false}, {"id": "transition-AgenticAI-1748495964798", "sequence": 3, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "AgenticAI", "tools_to_use": [{"tool_id": 1, "tool_name": "AgenticAI", "tool_params": {"items": [{"field_name": "model_provider", "data_type": "string", "field_value": null}, {"field_name": "base_url", "data_type": "string", "field_value": null}, {"field_name": "api_key", "data_type": "string", "field_value": null}, {"field_name": "model_name", "data_type": "string", "field_value": null}, {"field_name": "temperature", "data_type": "number", "field_value": null}, {"field_name": "objective", "data_type": "string", "field_value": "${objective}"}, {"field_name": "input_variables", "data_type": "object", "field_value": null}, {"field_name": "tools", "data_type": "array", "field_value": null}, {"field_name": "memory", "data_type": "string", "field_value": null}, {"field_name": "agent_type", "data_type": "string", "field_value": null}, {"field_name": "stream", "data_type": "boolean", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-ConditionalNode-1748495878571", "source_node_id": "Switch-Case Router", "data_type": "string", "mapping": [{"from_field": "condition_1_output", "to_field": "objective", "source_handle": "condition_1_output", "target_handle": "objective", "edge_id": "reactflow__edge-ConditionalNode-1748495878571condition_1_output-AgenticAI-1748495964798objective", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}, {"id": "transition-ApiRequestNode-1748495979157", "sequence": 4, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "ApiRequestNode", "tools_to_use": [{"tool_id": 1, "tool_name": "ApiRequestNode", "tool_params": {"items": [{"field_name": "url", "data_type": "string", "field_value": null}, {"field_name": "method", "data_type": "string", "field_value": null}, {"field_name": "query_params", "data_type": "object", "field_value": "${query_params}"}, {"field_name": "headers", "data_type": "object", "field_value": null}, {"field_name": "body", "data_type": "object", "field_value": null}, {"field_name": "timeout", "data_type": "number", "field_value": null}, {"field_name": "follow_redirects", "data_type": "boolean", "field_value": null}, {"field_name": "save_to_file", "data_type": "boolean", "field_value": null}, {"field_name": "output_format", "data_type": "string", "field_value": null}, {"field_name": "raise_on_error", "data_type": "boolean", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-ConditionalNode-1748495878571", "source_node_id": "Switch-Case Router", "data_type": "string", "mapping": [{"from_field": "condition_2_output", "to_field": "query_params", "source_handle": "condition_2_output", "target_handle": "query_params", "edge_id": "reactflow__edge-ConditionalNode-1748495878571condition_2_output-ApiRequestNode-1748495979157query_params", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}, {"id": "transition-AlterMetadataComponent-1748495993158", "sequence": 5, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "AlterMetadataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "AlterMetadataComponent", "tool_params": {"items": [{"field_name": "input_metadata", "data_type": "object", "field_value": "${input_metadata}"}, {"field_name": "updates", "data_type": "object", "field_value": null}, {"field_name": "keys_to_remove", "data_type": "array", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-ConditionalNode-1748495878571", "source_node_id": "Switch-Case Router", "data_type": "string", "mapping": [{"from_field": "default_output", "to_field": "input_metadata", "source_handle": "default_output", "target_handle": "input_metadata", "edge_id": "reactflow__edge-ConditionalNode-1748495878571default_output-AlterMetadataComponent-1748495993158input_metadata", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}]}