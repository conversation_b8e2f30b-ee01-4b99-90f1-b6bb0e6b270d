2025-05-29 10:33:46 - NodeExecutor - INFO - [setup_logger:467] Logger NodeExecutor configured with log file: logs\2025-05-29\NodeExecutor_10-33-46.log
2025-05-29 10:33:46 - NodeExecutor - INFO - [main_entry:179] Node Executor application starting
2025-05-29 10:33:46 - NodeExecutor - INFO - [main:70] Starting Node Executor
2025-05-29 10:33:46 - NodeExecutor - INFO - [main:71] Python version: 3.12.7 (tags/v3.12.7:0b05ead, Oct  1 2024, 03:06:41) [MSC v.1941 64 bit (AMD64)]
2025-05-29 10:33:46 - NodeExecutor - INFO - [main:72] Current working directory: C:\Users\<USER>\Desktop\ruh_ai\workflow_backend\node-executor-service
2025-05-29 10:33:46 - NodeExecutor - INFO - [main:77] Using Kafka bootstrap servers: 34.172.106.233:9092
2025-05-29 10:33:46 - NodeExecutor - INFO - [main:80] Initializing component manager
2025-05-29 10:33:46 - NodeExecutor - INFO - [main:82] Component manager initialized
2025-05-29 10:33:46 - NodeExecutor - INFO - [main:85] Setting up signal handlers for graceful shutdown
2025-05-29 10:33:46 - NodeExecutor - WARNING - [main:103] Cannot add signal handler for SIGINT on this platform.
2025-05-29 10:33:46 - NodeExecutor - WARNING - [main:103] Cannot add signal handler for SIGTERM on this platform.
2025-05-29 10:33:46 - NodeExecutor - INFO - [main:112] Dynamically discovering and importing components
2025-05-29 10:33:47 - NodeExecutor - INFO - [main:119] Dynamically imported 17 component modules
2025-05-29 10:33:47 - NodeExecutor - INFO - [main:122] Component registry after dynamic imports: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-05-29 10:33:47 - NodeExecutor - INFO - [main:124] Components imported successfully
2025-05-29 10:33:47 - NodeExecutor - INFO - [main:131] Registered components: []
2025-05-29 10:33:47 - NodeExecutor - INFO - [main:135] Initializing ToolExecutor
2025-05-29 10:33:47 - NodeExecutor - INFO - [main:138] ToolExecutor initialized successfully
2025-05-29 10:33:47 - NodeExecutor - INFO - [main:141] Starting the ApiRequestNode component as the main entry point for tool requests
