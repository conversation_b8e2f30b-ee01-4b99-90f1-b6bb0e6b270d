module.exports = {

"[project]/src/config/features.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Feature flags for the application
 *
 * These flags control which features are enabled in the application.
 * They can be overridden by environment variables.
 */ __turbopack_context__.s({
    "FEATURES": (()=>FEATURES)
});
const FEATURES = {
    // Validation features
    FRONTEND_VALIDATION: true,
    BACKEND_VALIDATION: false,
    HYBRID_VALIDATION: false,
    VALIDATION_DEBUG: process.env.NEXT_PUBLIC_VALIDATION_DEBUG === "true",
    // Validation behavior
    VALIDATE_ON_EDIT: false,
    VALIDATE_ON_SAVE: true,
    VALIDATE_ON_EXECUTE: true
};
}}),
"[project]/src/store/validationStore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useValidationStore": (()=>useValidationStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/validation/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$workflowValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/workflowValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$smartValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/smartValidation.ts [app-ssr] (ecmascript)");
;
;
;
const useValidationStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((set, get)=>({
        // Initial state
        isValid: true,
        errors: [],
        warnings: [],
        infos: [],
        missingFields: [],
        isValidating: false,
        hasValidated: false,
        // Actions
        validateWorkflow: (nodes, edges, options)=>{
            set({
                isValidating: true
            });
            // Perform validation
            const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$workflowValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateWorkflow"])(nodes, edges, options);
            // Update state with results
            set({
                isValid: result.isValid,
                errors: result.errors,
                warnings: result.warnings,
                infos: result.infos || [],
                missingFields: result.missingFields || [],
                startNodeId: result.startNodeId,
                connectedNodes: result.connectedNodes,
                isValidating: false,
                hasValidated: true
            });
            return result;
        },
        validateWorkflowSmart: async (nodes, edges, options)=>{
            set({
                isValidating: true
            });
            // Perform validation
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$smartValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateWorkflowSmart"])(nodes, edges, options);
            // Update state with results
            set({
                isValid: result.isValid,
                errors: result.errors,
                warnings: result.warnings,
                infos: result.infos || [],
                missingFields: result.missingFields || [],
                startNodeId: result.startNodeId,
                connectedNodes: result.connectedNodes,
                isValidating: false,
                hasValidated: true
            });
            return result;
        },
        validateBeforeSave: async (nodes, edges)=>{
            set({
                isValidating: true
            });
            // Perform validation
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$smartValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateWorkflowBeforeSave"])(nodes, edges);
            // Update state with results
            set({
                isValid: result.isValid,
                errors: result.errors,
                warnings: result.warnings,
                infos: result.infos || [],
                missingFields: result.missingFields || [],
                startNodeId: result.startNodeId,
                connectedNodes: result.connectedNodes,
                isValidating: false,
                hasValidated: true
            });
            return result;
        },
        validateBeforeExecution: async (nodes, edges)=>{
            set({
                isValidating: true
            });
            // Perform validation
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$smartValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateWorkflowBeforeExecution"])(nodes, edges);
            // Update state with results
            set({
                isValid: result.isValid,
                errors: result.errors,
                warnings: result.warnings,
                infos: result.infos || [],
                missingFields: result.missingFields || [],
                startNodeId: result.startNodeId,
                connectedNodes: result.connectedNodes,
                isValidating: false,
                hasValidated: true
            });
            return result;
        },
        validateDuringEditing: async (nodes, edges)=>{
            set({
                isValidating: true
            });
            // Perform validation
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$smartValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateWorkflowDuringEditing"])(nodes, edges);
            // Update state with results
            set({
                isValid: result.isValid,
                errors: result.errors,
                warnings: result.warnings,
                infos: result.infos || [],
                missingFields: result.missingFields || [],
                startNodeId: result.startNodeId,
                connectedNodes: result.connectedNodes,
                isValidating: false,
                hasValidated: true
            });
            return result;
        },
        clearValidation: ()=>{
            set({
                isValid: true,
                errors: [],
                warnings: [],
                infos: [],
                missingFields: [],
                startNodeId: undefined,
                connectedNodes: undefined,
                hasValidated: false
            });
        }
    }));
}}),
"[project]/src/store/executionStore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useExecutionStore": (()=>useExecutionStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const useExecutionStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Dialog state
        isDialogOpen: false,
        setDialogOpen: (isOpen)=>set((state)=>({
                    isDialogOpen: isOpen,
                    // If dialog is being opened and there's an active execution, update the state
                    hasActiveExecution: isOpen ? false : state.hasActiveExecution || state.isStreaming
                })),
        // Missing fields
        missingFields: [],
        setMissingFields: (fields)=>set({
                missingFields: fields
            }),
        // Tab state - default to parameters tab
        activeTab: "parameters",
        setActiveTab: (tab)=>set({
                activeTab: tab
            }),
        // Parameters state
        fieldValues: {},
        setFieldValues: (values)=>set({
                fieldValues: values
            }),
        updateFieldValue: (fieldId, value)=>set((state)=>({
                    fieldValues: {
                        ...state.fieldValues,
                        [fieldId]: value
                    }
                })),
        // Form validation
        errors: {},
        setErrors: (errors)=>set({
                errors
            }),
        isFormValid: false,
        setIsFormValid: (isValid)=>set({
                isFormValid: isValid
            }),
        // Logs state
        logs: [],
        setLogs: (logs)=>set({
                logs
            }),
        addLog: (log)=>set((state)=>({
                    logs: [
                        ...state.logs,
                        log
                    ]
                })),
        clearLogs: ()=>set({
                logs: []
            }),
        // Execution state
        isExecuting: false,
        setIsExecuting: (isExecuting)=>set({
                isExecuting
            }),
        // Correlation ID for SSE streaming
        correlationId: null,
        setCorrelationId: (id)=>set({
                correlationId: id
            }),
        // SSE connection state
        isStreaming: false,
        setIsStreaming: (isStreaming)=>set((state)=>({
                    isStreaming,
                    // Update hasActiveExecution based on streaming state
                    hasActiveExecution: !state.isDialogOpen && isStreaming ? true : state.hasActiveExecution && !isStreaming ? false : state.hasActiveExecution
                })),
        // Track if execution is ongoing but dialog is closed
        hasActiveExecution: false,
        setHasActiveExecution: (hasActive)=>set({
                hasActiveExecution: hasActive
            }),
        // Stop execution function - this will be called by the Stop Execution button
        stopExecution: ()=>{
            const state = get();
            // Add log entries
            const timestamp = new Date().toISOString().substring(11, 19); // Extract time HH:MM:SS
            // Add correlation ID to the log if available
            const correlationIdInfo = state.correlationId ? ` (Correlation ID: ${state.correlationId})` : '';
            const logEntry = `[${timestamp}] ⚠️ Workflow execution manually stopped by user${correlationIdInfo}`;
            const backendLogEntry = `[${timestamp}] 📡 Stop request sent to backend server`;
            // Update state
            set((state)=>({
                    logs: [
                        ...state.logs,
                        logEntry,
                        backendLogEntry
                    ],
                    isStreaming: false,
                    hasActiveExecution: false
                }));
        // Note: The actual SSE connection closing and backend API call are handled in the component
        },
        // View execution function - reopen dialog to view ongoing execution
        viewExecution: ()=>{
            console.log("Reopening execution dialog and resetting field values");
            // Reset field values to ensure fresh input values
            set((state)=>({
                    isDialogOpen: true,
                    activeTab: "logs",
                    hasActiveExecution: false,
                    fieldValues: {},
                    errors: {},
                    isFormValid: false
                }));
        },
        // Process values for execution
        processFieldValues: ()=>{
            const state = get();
            const processedValues = {};
            console.log("Processing field values from store:", state.fieldValues);
            state.missingFields.forEach((field)=>{
                const fieldId = `${field.nodeId}_${field.name}`;
                // Use the current value from the node's configuration if available
                // Otherwise, use the value from the form
                let value;
                if (field.isEmpty === false && field.currentValue !== undefined) {
                    value = field.currentValue;
                    console.log(`Using pre-configured value for field ${fieldId}:`, value);
                } else {
                    value = state.fieldValues[fieldId];
                    console.log(`Using form value for field ${fieldId}:`, value);
                }
                console.log(`Processing field ${fieldId} with value:`, value);
                // Parse JSON strings for object and array types
                if ((field.inputType === "object" || field.inputType === "dict" || field.inputType === "json" || field.inputType === "array" || field.inputType === "list") && typeof value === "string") {
                    try {
                        value = JSON.parse(value);
                        console.log(`Successfully parsed JSON for field ${fieldId}:`, value);
                    } catch (e) {
                        console.error(`Failed to parse JSON for field ${fieldId}:`, e);
                    }
                }
                // Convert string numbers to actual numbers
                if ((field.inputType === "number" || field.inputType === "int" || field.inputType === "float") && typeof value === "string") {
                    value = Number(value);
                    console.log(`Converted number field ${fieldId} to:`, value);
                }
                // Group values by node
                if (!processedValues[field.nodeId]) {
                    processedValues[field.nodeId] = {};
                }
                processedValues[field.nodeId][field.name] = value;
            });
            console.log("Final processed values for execution:", processedValues);
            return processedValues;
        },
        // Reset state
        resetState: ()=>{
            // Clear field values to ensure fresh input values each time
            console.log("Resetting execution store state - clearing all field values");
            set({
                activeTab: "parameters",
                fieldValues: {},
                errors: {},
                isFormValid: false,
                logs: [],
                isExecuting: false,
                missingFields: [],
                correlationId: null,
                isStreaming: false,
                hasActiveExecution: false
            });
        }
    }), {
    name: "execution-store",
    partialize: (state)=>({
            // Don't persist fieldValues to ensure fresh input values each time
            logs: state.logs
        })
}));
}}),
"[project]/src/store/mcpToolsStore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearAllMcpToolsState": (()=>clearAllMcpToolsState),
    "clearMcpToolsState": (()=>clearMcpToolsState),
    "getMcpToolsValue": (()=>getMcpToolsValue),
    "setMcpToolsValue": (()=>setMcpToolsValue),
    "useComponentStateStore": (()=>useComponentStateStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const useComponentStateStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        nodes: {},
        setValue: (nodeId, key, value)=>set((state)=>({
                    nodes: {
                        ...state.nodes,
                        [nodeId]: {
                            ...state.nodes[nodeId] || {},
                            [key]: value
                        }
                    }
                })),
        getValue: (nodeId, key, defaultValue = undefined)=>{
            const state = get();
            if (!state.nodes[nodeId]) return defaultValue;
            return state.nodes[nodeId][key] !== undefined ? state.nodes[nodeId][key] : defaultValue;
        },
        clearNodeState: (nodeId)=>set((state)=>{
                const newNodes = {
                    ...state.nodes
                };
                delete newNodes[nodeId];
                return {
                    nodes: newNodes
                };
            }),
        clearNodeKey: (nodeId, key)=>set((state)=>{
                if (!state.nodes[nodeId]) return state;
                const nodeState = {
                    ...state.nodes[nodeId]
                };
                delete nodeState[key];
                return {
                    nodes: {
                        ...state.nodes,
                        [nodeId]: nodeState
                    }
                };
            }),
        clearAllState: ()=>set({
                nodes: {}
            })
    }), {
    name: "component-state-store"
}));
const getMcpToolsValue = (nodeId, key, defaultValue)=>{
    return useComponentStateStore.getState().getValue(nodeId, key, defaultValue);
};
const setMcpToolsValue = (nodeId, key, value)=>{
    useComponentStateStore.getState().setValue(nodeId, key, value);
};
const clearMcpToolsState = (nodeId)=>{
    // Clear from Zustand store
    useComponentStateStore.getState().clearNodeState(nodeId);
    console.log(`MCP tools state cleared for node ${nodeId}`);
};
const clearAllMcpToolsState = ()=>{
    try {
        // Clear from local storage directly
        localStorage.removeItem("mcp-tools-store");
        // Also clear from the new store
        useComponentStateStore.getState().clearAllState();
        console.log("All MCP tools state cleared from local storage");
    } catch (error) {
        console.error("Error clearing MCP tools state from local storage:", error);
    }
};
}}),
"[project]/src/store/inspectorStore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useInspectorStore": (()=>useInspectorStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const useInspectorStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set)=>({
        // Default UI state
        activeTab: "settings",
        showValidation: false,
        validationErrors: {},
        // Default preferences
        preferences: {
            defaultTab: "settings",
            expandedSections: []
        },
        // Actions
        setActiveTab: (tab)=>set({
                activeTab: tab
            }),
        setShowValidation: (show)=>set({
                showValidation: show
            }),
        setValidationError: (inputName, error)=>set((state)=>({
                    validationErrors: {
                        ...state.validationErrors,
                        [inputName]: error
                    }
                })),
        clearValidationErrors: ()=>set({
                validationErrors: {},
                showValidation: false
            }),
        setPreference: (key, value)=>set((state)=>({
                    preferences: {
                        ...state.preferences,
                        [key]: value
                    }
                })),
        toggleExpandedSection: (sectionId)=>set((state)=>{
                const expandedSections = [
                    ...state.preferences.expandedSections
                ];
                const index = expandedSections.indexOf(sectionId);
                if (index === -1) {
                    expandedSections.push(sectionId);
                } else {
                    expandedSections.splice(index, 1);
                }
                return {
                    preferences: {
                        ...state.preferences,
                        expandedSections
                    }
                };
            })
    }), {
    name: "inspector-preferences",
    partialize: (state)=>({
            preferences: state.preferences
        })
}));
}}),
"[project]/src/hooks/useWorkflowValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useWorkflowValidation": (()=>useWorkflowValidation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reactflow/core/dist/esm/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$validationStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/validationStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
;
function useWorkflowValidation() {
    const { isValid, errors, warnings, infos, missingFields, hasValidated, isValidating, validateWorkflow, validateWorkflowSmart, validateBeforeSave, validateBeforeExecution, validateDuringEditing, clearValidation } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$validationStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useValidationStore"])();
    const { getNodes, getEdges } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useReactFlow"])();
    /**
   * Validate the current workflow
   */ const validateCurrentWorkflow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (options)=>{
        const nodes = getNodes();
        const edges = getEdges();
        return await validateWorkflowSmart(nodes, edges, options);
    }, [
        getNodes,
        getEdges,
        validateWorkflowSmart
    ]);
    /**
   * Validate the current workflow before saving
   */ const validateCurrentWorkflowBeforeSave = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        const nodes = getNodes();
        const edges = getEdges();
        return await validateBeforeSave(nodes, edges);
    }, [
        getNodes,
        getEdges,
        validateBeforeSave
    ]);
    /**
   * Validate the current workflow before execution
   *
   * @param providedNodes Optional nodes to use instead of getting from React Flow
   * @param providedEdges Optional edges to use instead of getting from React Flow
   */ const validateCurrentWorkflowBeforeExecution = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (providedNodes, providedEdges)=>{
        // Use provided nodes/edges if available, otherwise get from React Flow
        const nodes = providedNodes || getNodes();
        const edges = providedEdges || getEdges();
        console.log(`[useWorkflowValidation] Validating workflow with ${nodes.length} nodes and ${edges.length} edges`);
        // If nodes array is empty, log a warning
        if (nodes.length === 0) {
            console.warn('[useWorkflowValidation] WARNING: Empty nodes array for validation');
        }
        return await validateBeforeExecution(nodes, edges);
    }, [
        getNodes,
        getEdges,
        validateBeforeExecution
    ]);
    /**
   * Validate the current workflow during editing
   */ const validateCurrentWorkflowDuringEditing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        const nodes = getNodes();
        const edges = getEdges();
        return await validateDuringEditing(nodes, edges);
    }, [
        getNodes,
        getEdges,
        validateDuringEditing
    ]);
    /**
   * Validate a workflow with debouncing
   */ const debouncedValidate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debounce"])(async (nodes, edges, options)=>{
        await validateDuringEditing(nodes, edges);
    }, 500), [
        validateDuringEditing
    ]);
    /**
   * Get validation errors for a specific node
   */ const getNodeErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((nodeId)=>{
        return errors.filter((error)=>error.nodeId === nodeId);
    }, [
        errors
    ]);
    /**
   * Get validation errors for a specific field
   */ const getFieldErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((nodeId, fieldId)=>{
        return errors.filter((error)=>error.nodeId === nodeId && error.fieldId === fieldId);
    }, [
        errors
    ]);
    /**
   * Check if a node has any validation errors
   */ const hasNodeErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((nodeId)=>{
        return errors.some((error)=>error.nodeId === nodeId);
    }, [
        errors
    ]);
    /**
   * Check if a field has any validation errors
   */ const hasFieldErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((nodeId, fieldId)=>{
        return errors.some((error)=>error.nodeId === nodeId && error.fieldId === fieldId);
    }, [
        errors
    ]);
    /**
   * Get missing fields for a specific node
   */ const getNodeMissingFields = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((nodeId)=>{
        return missingFields.filter((field)=>field.nodeId === nodeId);
    }, [
        missingFields
    ]);
    return {
        // State
        isValid,
        errors,
        warnings,
        infos,
        missingFields,
        hasValidated,
        isValidating,
        // Actions
        validateWorkflow,
        validateWorkflowSmart,
        validateCurrentWorkflow,
        validateBeforeSave,
        validateBeforeExecution,
        validateDuringEditing,
        validateCurrentWorkflowBeforeSave,
        validateCurrentWorkflowBeforeExecution,
        validateCurrentWorkflowDuringEditing,
        debouncedValidate,
        clearValidation,
        // Helpers
        getNodeErrors,
        getFieldErrors,
        hasNodeErrors,
        hasFieldErrors,
        getNodeMissingFields
    };
}
}}),
"[project]/src/hooks/useConnectedHandles.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useConnectedHandles": (()=>useConnectedHandles)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function useConnectedHandles(selectedNode, edges, nodes) {
    // State to track connected input handles
    const [connectedInputs, setConnectedInputs] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    // State to track connection sources
    const [connectionSources, setConnectionSources] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    // Update connected inputs whenever the selected node or edges change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!selectedNode) {
            setConnectedInputs({});
            setConnectionSources({});
            return;
        }
        // Find all edges that target the selected node
        const nodeInputEdges = edges.filter((edge)=>edge.target === selectedNode.id);
        // Create a map of input handle names to connection status
        const connectedHandles = {};
        const sources = {};
        // Mark all handles as disconnected initially
        if (selectedNode.data.definition?.inputs) {
            selectedNode.data.definition.inputs.forEach((input)=>{
                if (input.is_handle) {
                    connectedHandles[input.name] = false;
                }
            });
            // Also check for dynamic inputs in the config
            if (selectedNode.data.config?.inputs) {
                selectedNode.data.config.inputs.forEach((input)=>{
                    if (input.is_handle) {
                        connectedHandles[input.name] = false;
                    }
                });
            }
        }
        // Mark connected handles and store connection sources
        nodeInputEdges.forEach((edge)=>{
            if (edge.targetHandle) {
                connectedHandles[edge.targetHandle] = true;
                if (edge.source && edge.sourceHandle) {
                    sources[edge.targetHandle] = {
                        nodeId: edge.source,
                        handleId: edge.sourceHandle
                    };
                }
            }
        });
        setConnectedInputs(connectedHandles);
        setConnectionSources(sources);
    }, [
        selectedNode,
        edges
    ]);
    // Helper function to check if a specific input is connected
    const isInputConnected = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((inputName)=>{
        return !!connectedInputs[inputName];
    }, [
        connectedInputs
    ]);
    // Helper function to check if an input should be disabled
    // This handles both direct inputs and handle inputs
    const shouldDisableInput = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((inputName)=>{
        // If this is a handle input, it should never be disabled
        if (inputName.endsWith("_handle")) {
            return false;
        }
        // For inputs with is_handle=true, check if they're connected
        if (isInputConnected(inputName)) {
            return true;
        }
        // Check if there's a corresponding handle input
        const handleName = `${inputName}_handle`;
        // If the handle is connected, disable the direct input
        return isInputConnected(handleName);
    }, [
        connectedInputs,
        isInputConnected
    ]);
    // Helper function to get the source node and handle for a connected input
    const getConnectedSource = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((inputName)=>{
        return connectionSources[inputName] || null;
    }, [
        connectionSources
    ]);
    // Helper function to get detailed connection information
    const getConnectionInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((inputName)=>{
        const isConnected = isInputConnected(inputName);
        if (!isConnected) {
            return {
                isConnected: false
            };
        }
        const source = getConnectedSource(inputName);
        if (!source) {
            return {
                isConnected: true
            };
        }
        // Find the source node
        const sourceNode = nodes.find((node)=>node.id === source.nodeId);
        if (!sourceNode) {
            return {
                isConnected: true,
                sourceNodeId: source.nodeId
            };
        }
        return {
            isConnected: true,
            sourceNodeId: source.nodeId,
            sourceNodeType: sourceNode.data.type,
            sourceNodeLabel: sourceNode.data.label || sourceNode.data.type
        };
    }, [
        isInputConnected,
        getConnectedSource,
        nodes
    ]);
    return {
        connectedInputs,
        isInputConnected,
        shouldDisableInput,
        getConnectedSource,
        getConnectionInfo
    };
}
}}),
"[project]/src/utils/visibility-rules.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "evaluateVisibilityRule": (()=>evaluateVisibilityRule),
    "evaluateVisibilityRules": (()=>evaluateVisibilityRules),
    "filterVisibleInputs": (()=>filterVisibleInputs),
    "shouldShowInput": (()=>shouldShowInput)
});
function evaluateVisibilityRule(rule, config) {
    // Get the target field value from config
    const targetValue = config?.[rule.field_name];
    // Simple equality check
    if (rule.operator === undefined || rule.operator === "equals") {
        return targetValue === rule.field_value;
    }
    // Not equals
    if (rule.operator === "not_equals") {
        return targetValue !== rule.field_value;
    }
    // Contains (for arrays and strings)
    if (rule.operator === "contains") {
        if (Array.isArray(targetValue)) {
            return targetValue.includes(rule.field_value);
        }
        if (typeof targetValue === "string") {
            return targetValue.includes(String(rule.field_value));
        }
        return false;
    }
    // Greater than (for numbers)
    if (rule.operator === "greater_than") {
        return typeof targetValue === "number" && typeof rule.field_value === "number" && targetValue > rule.field_value;
    }
    // Less than (for numbers)
    if (rule.operator === "less_than") {
        return typeof targetValue === "number" && typeof rule.field_value === "number" && targetValue < rule.field_value;
    }
    // Exists (field exists and is not null/undefined)
    if (rule.operator === "exists") {
        return targetValue !== undefined && targetValue !== null;
    }
    // Not exists (field doesn't exist or is null/undefined)
    if (rule.operator === "not_exists") {
        return targetValue === undefined || targetValue === null;
    }
    // Default to false for unknown operators
    return false;
}
function evaluateVisibilityRules(rules, config, logicOperator = "OR") {
    // If no rules, always show
    if (!rules || rules.length === 0) {
        return true;
    }
    // Evaluate each rule
    const results = rules.map((rule)=>evaluateVisibilityRule(rule, config));
    // Combine results based on logic operator
    if (logicOperator === "AND") {
        return results.every((result)=>result);
    } else {
        return results.some((result)=>result);
    }
}
function shouldShowInput(input, config) {
    // If no visibility rules, always show
    if (!input.visibility_rules || input.visibility_rules.length === 0) {
        return true;
    }
    // Get the logic operator from the input definition or default to 'OR'
    const logicOperator = input.visibility_logic || "OR";
    // Evaluate the visibility rules
    return evaluateVisibilityRules(input.visibility_rules, config, logicOperator);
}
function filterVisibleInputs(inputs, config) {
    return inputs.filter((input)=>shouldShowInput(input, config));
}
}}),
"[project]/src/utils/inputVisibility.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkInputVisibility": (()=>checkInputVisibility)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$visibility$2d$rules$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/visibility-rules.ts [app-ssr] (ecmascript)");
;
function checkInputVisibility(inputDef, node, config) {
    if (!node) return false;
    // Special handling for DynamicCombineTextComponent
    if (node.data.type === "DynamicCombineTextComponent" && inputDef.name.startsWith("input_") && !inputDef.is_handle) {
        // Extract the index from the input name (e.g., "input_3" -> 3)
        const match = inputDef.name.match(/input_(\d+)/);
        if (match && match[1]) {
            const inputIndex = parseInt(match[1], 10);
            const numAdditionalInputs = parseInt(config.num_additional_inputs || "0", 10);
            // Show the input if its index is less than or equal to the number of additional inputs
            return inputIndex <= numAdditionalInputs;
        }
    }
    // Special handling for ConditionalNode
    if (node.data.originalType === "ConditionalNode") {
        // Handle dynamic condition inputs (condition_3_*, condition_4_*, etc.)
        const conditionMatch = inputDef.name.match(/condition_(\d+)_/);
        if (conditionMatch && conditionMatch[1]) {
            const conditionIndex = parseInt(conditionMatch[1], 10);
            const numAdditionalConditions = parseInt(config.num_additional_conditions || "0", 10);
            const totalConditions = 2 + numAdditionalConditions; // Base 2 + additional
            // For conditions 3 and above, check if they should be visible
            if (conditionIndex > 2) {
                // Show if condition index is within total conditions
                if (conditionIndex > totalConditions) {
                    return false;
                }
            }
            // For input handles, also check the source setting
            if (inputDef.name.endsWith("_input_handle")) {
                const sourceValue = config[`condition_${conditionIndex}_source`] || "node_output";
                return sourceValue === "node_output";
            }
        }
    }
    // Special handling for MCP Marketplace components
    if (isMCPMarketplaceComponent(node)) {
        return checkMCPMarketplaceInputVisibility(inputDef, node, config);
    }
    // Special handling for MCP Tools component
    if (node.data.type === "MCPToolsComponent") {
        return checkMCPToolsInputVisibility(inputDef, config);
    }
    // Use the utility function for standard visibility rules
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$visibility$2d$rules$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shouldShowInput"])(inputDef, config);
}
/**
 * Checks if a node is an MCP Marketplace component
 */ function isMCPMarketplaceComponent(node) {
    if (!node) return false;
    return node.data.type === "MCPMarketplaceComponent" || node.data.definition?.category === "MCP Marketplace";
}
/**
 * Checks visibility for MCP Marketplace component inputs
 */ function checkMCPMarketplaceInputVisibility(inputDef, node, config) {
    // For explicit handle inputs (ending with _handle), always show them
    if (inputDef.input_type === "handle" || inputDef.name.endsWith("_handle")) {
        // Always show explicit handle inputs
        return true;
    }
    // Hide connection fields that have a direct input equivalent
    if (inputDef.name.endsWith("_connection")) {
        // Check if there's a direct input with the same base name
        const baseName = inputDef.name.replace("_connection", "");
        const hasDirectInput = node.data?.definition?.inputs?.some((input)=>input.name === baseName) || false;
        if (hasDirectInput) {
            return false;
        }
    }
    // For inputs with is_handle=true, always show them
    if (inputDef.is_handle) {
        return true;
    }
    // For regular inputs, check if there's a corresponding handle
    if (hasCorrespondingHandle(inputDef.name, node)) {
        // If the handle is connected, hide the direct input
        // This would require the isInputConnected function from useConnectedHandles
        // For now, we'll return true and handle this in the component
        return true;
    }
    // Default to showing the input
    return true;
}
/**
 * Checks if an input has a corresponding handle
 */ function hasCorrespondingHandle(inputName, node) {
    if (!node?.data?.definition?.inputs) return false;
    // Check for handle with a suffix pattern (e.g., input_dict_handle for input_dict)
    const handleSuffix = node.data.definition.inputs.find((input)=>(input.is_handle || input.input_type === "handle") && input.name === `${inputName}_handle`);
    return !!handleSuffix;
}
/**
 * Checks visibility for MCP Tools component inputs
 */ function checkMCPToolsInputVisibility(inputDef, config) {
    // For selected_tool_name, check connection_status
    if (inputDef.name === "selected_tool_name") {
        // Check connection status
        const connectionStatus = config.connection_status || "Not Connected";
        // Show if connected
        return connectionStatus === "Connected";
    }
    // For refresh_tools and disconnect buttons, always hide them
    if (inputDef.name === "refresh_tools" || inputDef.name === "disconnect") {
        return false; // Always hide these buttons
    }
    // Use the utility function for standard visibility rules
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$visibility$2d$rules$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shouldShowInput"])(inputDef, config);
}
}}),
"[project]/src/utils/inputValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createNodeConfigSchema": (()=>createNodeConfigSchema),
    "createSchemaForInput": (()=>createSchemaForInput),
    "validateAllInputs": (()=>validateAllInputs),
    "validateInput": (()=>validateInput)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-ssr] (ecmascript)");
;
function validateInput(inputDef, value) {
    // Skip validation for empty optional inputs
    if (!inputDef.required && (value === undefined || value === null || value === "")) {
        return {
            isValid: true,
            message: ""
        };
    }
    // Validate based on input type
    switch(inputDef.input_type){
        case "string":
            return validateString(inputDef, value);
        case "int":
        case "float":
        case "number":
            return validateNumber(inputDef, value);
        case "list":
        case "array":
            return validateArray(inputDef, value);
        case "dict":
        case "json":
        case "object":
            return validateObject(inputDef, value);
        default:
            // For other types, assume valid
            return {
                isValid: true,
                message: ""
            };
    }
}
/**
 * Validates a string input
 */ function validateString(inputDef, value) {
    if (typeof value !== "string") {
        return {
            isValid: false,
            message: "Must be a string"
        };
    }
    const minLength = inputDef.min_length || 0;
    const maxLength = inputDef.max_length || Number.MAX_SAFE_INTEGER;
    if (value.length < minLength) {
        return {
            isValid: false,
            message: `Must be at least ${minLength} characters`
        };
    }
    if (value.length > maxLength) {
        return {
            isValid: false,
            message: `Must be at most ${maxLength} characters`
        };
    }
    // Check pattern if specified
    if (inputDef.pattern) {
        try {
            const regex = new RegExp(inputDef.pattern);
            if (!regex.test(value)) {
                return {
                    isValid: false,
                    message: inputDef.pattern_error || "Invalid format"
                };
            }
        } catch (e) {
            console.error("Invalid regex pattern:", inputDef.pattern);
        }
    }
    return {
        isValid: true,
        message: "Valid input"
    };
}
/**
 * Validates a number input
 */ function validateNumber(inputDef, value) {
    const numValue = Number(value);
    if (isNaN(numValue)) {
        return {
            isValid: false,
            message: "Must be a number"
        };
    }
    const minValue = inputDef.min_value !== undefined ? Number(inputDef.min_value) : Number.MIN_SAFE_INTEGER;
    const maxValue = inputDef.max_value !== undefined ? Number(inputDef.max_value) : Number.MAX_SAFE_INTEGER;
    if (numValue < minValue) {
        return {
            isValid: false,
            message: `Must be at least ${minValue}`
        };
    }
    if (numValue > maxValue) {
        return {
            isValid: false,
            message: `Must be at most ${maxValue}`
        };
    }
    return {
        isValid: true,
        message: "Valid number"
    };
}
/**
 * Validates an array input
 */ function validateArray(inputDef, value) {
    // Check if it's a list
    let listValue = value;
    if (typeof value === "string") {
        try {
            listValue = JSON.parse(value);
        } catch (e) {
            return {
                isValid: false,
                message: "Invalid JSON format"
            };
        }
    }
    if (!Array.isArray(listValue)) {
        return {
            isValid: false,
            message: "Must be an array"
        };
    }
    // Check min/max items
    const minItems = inputDef.min_items || 0;
    const maxItems = inputDef.max_items || Number.MAX_SAFE_INTEGER;
    if (listValue.length < minItems) {
        return {
            isValid: false,
            message: `Must have at least ${minItems} items`
        };
    }
    if (listValue.length > maxItems) {
        return {
            isValid: false,
            message: `Must have at most ${maxItems} items`
        };
    }
    return {
        isValid: true,
        message: "Valid list"
    };
}
/**
 * Validates an object input
 */ function validateObject(inputDef, value) {
    // Check if it's a valid JSON object
    let dictValue = value;
    if (typeof value === "string") {
        try {
            dictValue = JSON.parse(value);
        } catch (e) {
            return {
                isValid: false,
                message: "Invalid JSON format"
            };
        }
    }
    if (typeof dictValue !== "object" || dictValue === null || Array.isArray(dictValue)) {
        return {
            isValid: false,
            message: "Must be an object"
        };
    }
    // Check required keys
    const requiredKeys = inputDef.required_keys || [];
    for (const key of requiredKeys){
        if (!(key in dictValue)) {
            return {
                isValid: false,
                message: `Missing required key: ${key}`
            };
        }
    }
    return {
        isValid: true,
        message: "Valid object"
    };
}
function validateAllInputs(inputs, config) {
    const errors = {};
    // Validate each input
    inputs.forEach((inputDef)=>{
        // Skip handle inputs
        if (inputDef.is_handle) return;
        // Get current value
        const value = config[inputDef.name];
        // Validate
        errors[inputDef.name] = validateInput(inputDef, value);
    });
    return errors;
}
function createSchemaForInput(inputDef) {
    switch(inputDef.input_type){
        case "string":
            let schema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string();
            if (inputDef.required) {
                schema = schema.min(1, {
                    message: "This field is required"
                });
            } else {
                schema = schema.optional();
            }
            if (inputDef.min_length) {
                schema = schema.min(inputDef.min_length, {
                    message: `Must be at least ${inputDef.min_length} characters`
                });
            }
            if (inputDef.max_length) {
                schema = schema.max(inputDef.max_length, {
                    message: `Must be at most ${inputDef.max_length} characters`
                });
            }
            if (inputDef.pattern) {
                schema = schema.regex(new RegExp(inputDef.pattern), {
                    message: inputDef.pattern_error || "Invalid format"
                });
            }
            return schema;
        case "int":
            let intSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].coerce.number().int();
            if (!inputDef.required) {
                intSchema = intSchema.optional();
            }
            if (inputDef.min_value !== undefined) {
                intSchema = intSchema.min(Number(inputDef.min_value), {
                    message: `Must be at least ${inputDef.min_value}`
                });
            }
            if (inputDef.max_value !== undefined) {
                intSchema = intSchema.max(Number(inputDef.max_value), {
                    message: `Must be at most ${inputDef.max_value}`
                });
            }
            return intSchema;
        case "float":
        case "number":
            let numSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].coerce.number();
            if (!inputDef.required) {
                numSchema = numSchema.optional();
            }
            if (inputDef.min_value !== undefined) {
                numSchema = numSchema.min(Number(inputDef.min_value), {
                    message: `Must be at least ${inputDef.min_value}`
                });
            }
            if (inputDef.max_value !== undefined) {
                numSchema = numSchema.max(Number(inputDef.max_value), {
                    message: `Must be at most ${inputDef.max_value}`
                });
            }
            return numSchema;
        case "bool":
            return inputDef.required ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].boolean() : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].boolean().optional();
        case "list":
        case "array":
            let arraySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].any());
            if (!inputDef.required) {
                arraySchema = arraySchema.optional();
            }
            if (inputDef.min_items !== undefined) {
                arraySchema = arraySchema.min(inputDef.min_items, {
                    message: `Must have at least ${inputDef.min_items} items`
                });
            }
            if (inputDef.max_items !== undefined) {
                arraySchema = arraySchema.max(inputDef.max_items, {
                    message: `Must have at most ${inputDef.max_items} items`
                });
            }
            return arraySchema;
        case "dict":
        case "json":
        case "object":
            let objSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].record(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].any());
            if (!inputDef.required) {
                objSchema = objSchema.optional();
            }
            // Add validation for required keys if specified
            if (inputDef.required_keys && inputDef.required_keys.length > 0) {
                // This is a simplified approach - for more complex validation,
                // we would need to create a proper object schema with all properties
                objSchema = objSchema.refine((obj)=>{
                    if (!obj) return false;
                    return inputDef.required_keys.every((key)=>key in obj);
                }, {
                    message: `Missing required keys: ${inputDef.required_keys.join(", ")}`
                });
            }
            return objSchema;
        default:
            // For other types, use any schema
            return inputDef.required ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].any() : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].any().optional();
    }
}
function createNodeConfigSchema(inputs) {
    const shape = {};
    inputs.forEach((input)=>{
        // Skip handle inputs
        if (input.is_handle) return;
        shape[input.name] = createSchemaForInput(input);
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object(shape);
}
}}),
"[project]/src/utils/valueFormatting.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "formatValueForDisplay": (()=>formatValueForDisplay),
    "getConfigValue": (()=>getConfigValue),
    "getHtmlInputType": (()=>getHtmlInputType),
    "parseInputValue": (()=>parseInputValue)
});
function formatValueForDisplay(value, inputType) {
    if (value === undefined || value === null) {
        return "";
    }
    switch(inputType){
        case "object":
        case "dict":
        case "json":
        case "list":
        case "array":
            return typeof value === "object" ? JSON.stringify(value, null, 2) : String(value);
        case "bool":
            return value ? "True" : "False";
        default:
            return String(value);
    }
}
function parseInputValue(value, inputType) {
    switch(inputType){
        case "int":
            return parseInt(value, 10);
        case "float":
        case "number":
            return parseFloat(value);
        case "bool":
            return value === "true" || value === "True" || value === "1";
        case "object":
        case "dict":
        case "json":
        case "list":
        case "array":
            try {
                return JSON.parse(value);
            } catch (e) {
                return value; // Return as string if parsing fails
            }
        default:
            return value;
    }
}
function getHtmlInputType(inputType) {
    switch(inputType){
        case "int":
        case "float":
        case "number":
            return "number";
        case "password":
            return "password";
        default:
            return "text";
    }
}
function getConfigValue(inputName, defaultValue, config, nodeType, nodeId, getStoreValue) {
    // Special handling for MCP Tools component
    if (nodeType === "MCPToolsComponent" && getStoreValue) {
        if (inputName === "sse_url") {
            return getStoreValue(nodeId, "sse_url", config?.sse_url || defaultValue);
        }
        if (inputName === "mcp_type") {
            return getStoreValue(nodeId, "mcp_type", config?.mcp_type || defaultValue);
        }
        if (inputName === "selected_tool_name") {
            return getStoreValue(nodeId, "selected_tool_name", config?.selected_tool_name || defaultValue);
        }
        // For connection_status, check if it's in the raw inputs
        if (inputName === "connection_status") {
            const rawInputs = config?.inputs || [];
            const statusInput = rawInputs.find((input)=>input.name === "connection_status");
            if (statusInput && statusInput.value) {
                return statusInput.value;
            }
            if (config?.connection_status) {
                return config.connection_status;
            }
        }
    }
    // Default behavior for other components and inputs
    return config?.[inputName] ?? defaultValue;
}
}}),
"[project]/src/app/(features)/workflows/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Workflow API Module
 *
 * This module provides functions for interacting with the workflow API endpoints.
 */ __turbopack_context__.s({
    "createEmptyWorkflow": (()=>createEmptyWorkflow),
    "default": (()=>__TURBOPACK__default__export__),
    "deleteWorkflow": (()=>deleteWorkflow),
    "fetchWorkflowById": (()=>fetchWorkflowById),
    "fetchWorkflowFromBuilderUrl": (()=>fetchWorkflowFromBuilderUrl),
    "fetchWorkflowsByUser": (()=>fetchWorkflowsByUser),
    "updateWorkflowMetadata": (()=>updateWorkflowMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/axios.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cookies.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/clientCookies.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiConfig.ts [app-ssr] (ecmascript)");
;
;
;
;
async function fetchWorkflowsByUser(page = 1, pageSize = 10, accessToken) {
    try {
        console.log(`[DEBUG] Fetching workflows with page=${page}, pageSize=${pageSize}`);
        console.log(`[DEBUG] Using endpoint: ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOWS.LIST}`);
        // If no token is provided, try to get it from client or server
        if (!accessToken) {
            try {
                if ("TURBOPACK compile-time falsy", 0) {
                    "TURBOPACK unreachable";
                } else {
                    // Server-side
                    accessToken = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
                    console.log(`[DEBUG] Retrieved server token (length: ${accessToken ? accessToken.length : 0})`);
                }
            } catch (tokenError) {
                console.error(`[DEBUG] Error retrieving token:`, tokenError);
            }
        } else {
            console.log(`[DEBUG] Using provided access token (length: ${accessToken.length})`);
        }
        // Prepare config with authentication if token is provided
        const config = {
            headers: {
                "Content-Type": "application/json"
            },
            withCredentials: false,
            params: {
                page: page,
                page_size: pageSize
            }
        };
        // Add Authorization header if access token is provided
        if (accessToken) {
            // Ensure token has Bearer prefix
            if (accessToken.startsWith("Bearer ")) {
                config.headers.Authorization = accessToken;
            } else {
                config.headers.Authorization = `Bearer ${accessToken}`;
            }
            console.log(`[DEBUG] Added Authorization header`);
        } else {
            console.warn(`[DEBUG] No access token available for request`);
        }
        // Use the centralized workflowApi instance that has the interceptors
        // and the correct endpoint from API_ENDPOINTS
        console.log(`[DEBUG] Making request to: ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOWS.LIST}`);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["workflowApi"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOWS.LIST, config);
        console.log(`[DEBUG] Successful response with ${response.data?.data?.length || 0} workflows`);
        return response.data;
    } catch (error) {
        console.error("Error fetching workflows:", error);
        // Add more detailed error logging
        if (error.response) {
            console.error(`[DEBUG] Response status: ${error.response.status}`);
            console.error(`[DEBUG] Response data:`, error.response.data);
            throw new Error(`Failed to fetch workflows: ${error.response.status} ${error.response.statusText}`);
        } else if (error.request) {
            // The request was made but no response was received
            console.error(`[DEBUG] No response received:`, error.request);
            throw new Error("Failed to fetch workflows: No response received from server");
        } else {
            // Something happened in setting up the request
            console.error(`[DEBUG] Request setup error:`, error.message);
            throw new Error(`Failed to fetch workflows: ${error.message}`);
        }
    }
}
async function createEmptyWorkflow() {
    try {
        // Create a StartNode for the initial workflow
        const startNode = {
            id: "start-node",
            type: "WorkflowNode",
            position: {
                x: 100,
                y: 100
            },
            data: {
                label: "Start",
                type: "component",
                originalType: "StartNode",
                definition: {
                    name: "StartNode",
                    display_name: "Start",
                    description: "The starting point for all workflows. Only nodes connected to this node will be executed.",
                    category: "Input/Output",
                    icon: "Play",
                    beta: false,
                    inputs: [],
                    outputs: [
                        {
                            name: "flow",
                            display_name: "Flow",
                            output_type: "Any"
                        }
                    ],
                    is_valid: true,
                    path: "components.io.start_node"
                },
                config: {
                    collected_parameters: {}
                }
            }
        };
        const payload = {
            name: "Untitled Workflow",
            description: "New workflow",
            workflow_data: {
                nodes: [
                    startNode
                ],
                edges: []
            },
            start_node_data: []
        };
        // Get token for authorization
        let token;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Server-side
            token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
        }
        const config = {
            headers: {
                "Content-Type": "application/json",
                ...token && {
                    Authorization: `Bearer ${token}`
                }
            },
            withCredentials: false
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["workflowApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOWS.CREATE, payload, config);
        return response.data;
    } catch (error) {
        console.error("Error creating workflow:", error);
        if (error.response) {
            throw new Error(`Failed to create workflow: ${error.response.status} ${error.response.statusText}`);
        }
        throw error;
    }
}
async function fetchWorkflowById(id) {
    try {
        // Get token for authorization
        let token;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Server-side
            token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
        }
        const config = {
            headers: {
                "Content-Type": "application/json",
                ...token && {
                    Authorization: `Bearer ${token}`
                }
            },
            withCredentials: false
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["workflowApi"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOWS.GET(id), config);
        console.log("Workflow API response:", response.data);
        return response.data;
    } catch (error) {
        console.error("Error fetching workflow:", error);
        if (error.response) {
            throw new Error(`Failed to fetch workflow: ${error.response.status} ${error.response.statusText}`);
        }
        throw error;
    }
}
async function fetchWorkflowFromBuilderUrl(url) {
    try {
        // For external URLs, we need to use the external API instance (no auth)
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["externalApi"].get(url, {
            headers: {
                "Content-Type": "application/json"
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching workflow from builder URL:", error);
        if (error.response) {
            throw new Error(`Failed to fetch workflow data: ${error.response.status} ${error.response.statusText}`);
        }
        throw error;
    }
}
async function deleteWorkflow(id) {
    try {
        // Get token for authorization
        let token;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Server-side
            token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
        }
        const config = {
            headers: {
                "Content-Type": "application/json",
                ...token && {
                    Authorization: `Bearer ${token}`
                }
            },
            withCredentials: false
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["workflowApi"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOWS.DELETE(id), config);
        console.log("Workflow delete response:", response.data);
        return {
            success: true,
            message: response.data?.message || "Workflow deleted successfully"
        };
    } catch (error) {
        console.error("Error deleting workflow:", error);
        if (error.response) {
            throw new Error(`Failed to delete workflow: ${error.response.status} ${error.response.statusText}`);
        }
        throw error;
    }
}
async function updateWorkflowMetadata(id, data) {
    try {
        // Get token for authorization
        let token;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Server-side
            token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
        }
        const config = {
            headers: {
                "Content-Type": "application/json",
                ...token && {
                    Authorization: `Bearer ${token}`
                }
            },
            withCredentials: false
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["workflowApi"].patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOWS.UPDATE(id), data, config);
        return response.data;
    } catch (error) {
        console.error("Error updating workflow metadata:", error);
        if (error.response) {
            throw new Error(`Failed to update workflow: ${error.response.status} ${error.response.statusText}`);
        }
        throw error;
    }
}
const workflowApiExports = {
    fetchWorkflowsByUser,
    createEmptyWorkflow,
    fetchWorkflowById,
    fetchWorkflowFromBuilderUrl,
    deleteWorkflow,
    updateWorkflowMetadata
};
const __TURBOPACK__default__export__ = workflowApiExports;
}}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// frontend\src\app\page.tsx
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Sidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/Sidebar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$TopBar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/TopBar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$canvas$2f$WorkflowCanvas$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/canvas/WorkflowCanvas.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$workflowUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/workflowUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$workflowApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/workflowApi.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$features$292f$workflows$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/(features)/workflows/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$workflowApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/workflowApi.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$modals$2f$LoadWorkflowModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/modals/LoadWorkflowModal.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$modals$2f$UnsavedChangesDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/modals/UnsavedChangesDialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$validationStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/validationStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$executionStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/executionStore.ts [app-ssr] (ecmascript)");
"use client"; // Required for hooks like useState, useEffect
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Create a separate component for the part that uses useSearchParams
function WorkflowLoader({ onWorkflowLoad, onWorkflowIdChange, onLoadingChange, onErrorChange, workflowId, loadedWorkflow }) {
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const loadWorkflowFromId = async ()=>{
            const id = searchParams.get("workflow_id");
            if (!id) {
                // Redirect to workflows page if no workflow_id is provided
                router.push("/workflows");
                return;
            }
            // Skip if we've already loaded this workflow ID
            if (id === workflowId && loadedWorkflow) {
                console.log("Workflow already loaded, skipping fetch");
                return;
            }
            onWorkflowIdChange(id);
            onLoadingChange(true);
            onErrorChange(null);
            try {
                // API URL is configured in the fetchWorkflowById function
                // Fetch workflow details to get the builder_url
                const workflowDetails = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$features$292f$workflows$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchWorkflowById"])(id);
                console.log("Workflow details:", workflowDetails);
                if (!workflowDetails || !workflowDetails.workflow || !workflowDetails.workflow.builder_url) {
                    console.error("Workflow data URL not found inside workflow details.", workflowDetails?.workflow?.builder_url);
                    throw new Error("Workflow data URL not found");
                }
                // Fetch the actual workflow data from builder_url
                const workflowData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$workflowApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["fetchWorkflowFromBuilderUrl"])(workflowDetails.workflow.builder_url);
                // Ensure the workflow_name is set in the workflowData
                if (!workflowData.workflow_name && workflowDetails.workflow.name) {
                    console.log("Setting workflow_name from workflowDetails:", workflowDetails.workflow.name);
                    workflowData.workflow_name = workflowDetails.workflow.name;
                }
                console.log("Final workflowData before loading:", workflowData);
                // Load the workflow into the canvas without showing success message
                // Pass false to prevent showing the success notification when loading from URL
                onWorkflowLoad(workflowData, false);
            } catch (error) {
                console.error("Error loading workflow:", error);
                onErrorChange(error instanceof Error ? error.message : "Failed to load workflow. Please try again.");
            } finally{
                onLoadingChange(false);
            }
        };
        loadWorkflowFromId();
    }, [
        searchParams,
        onWorkflowLoad,
        workflowId,
        loadedWorkflow,
        router,
        onWorkflowIdChange,
        onLoadingChange,
        onErrorChange
    ]);
    return null; // This component doesn't render anything
}
function Home() {
    // We don't need router in this component as redirection is handled in WorkflowLoader
    // State for loading components
    const [componentsData, setComponentsData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    // Memoize components to prevent unnecessary re-renders of the Sidebar
    const components = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>componentsData, [
        componentsData
    ]);
    // Rename for clarity
    const [isLoadingComponents, setIsLoadingComponents] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true); // <<< RENAME
    const [componentError, setComponentError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null); // <<< RENAME
    // State for the workflow itself
    const [currentNodes, setCurrentNodes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [currentEdges, setCurrentEdges] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // State for workflow title and theme
    const [workflowTitle, setWorkflowTitle] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("Untitled Workflow");
    const [isDarkMode, setIsDarkMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // State for execution status
    const [isExecuting, setIsExecuting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [executionResult, setExecutionResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // State for workflow loading
    const [isLoadModalOpen, setIsLoadModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [loadedWorkflow, setLoadedWorkflow] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // State for sidebar collapse
    const [isSidebarCollapsed, setIsSidebarCollapsed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // State for unsaved changes dialog
    const [isUnsavedChangesDialogOpen, setIsUnsavedChangesDialogOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [pendingAction, setPendingAction] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // State for workflow ID and loading from URL
    const [workflowId, setWorkflowId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoadingWorkflow, setIsLoadingWorkflow] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [workflowLoadError, setWorkflowLoadError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // State for tracking unsaved changes
    const [hasUnsavedChanges, setHasUnsavedChanges] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Effect to load components on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const loadComponents = async ()=>{
            try {
                setIsLoadingComponents(true); // <<< Use renamed state
                setComponentError(null); // <<< Use renamed state
                // Fetch regular components
                const fetchedComponents = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchComponents"])();
                console.log("DEBUG: Fetched regular components categories:", Object.keys(fetchedComponents));
                // Log AI components if they exist
                if (fetchedComponents.AI) {
                    console.log("DEBUG: AI components found:", Object.keys(fetchedComponents.AI));
                } else {
                    console.log("DEBUG: No AI category found in components");
                }
                // Fetch MCP components
                const mcpComponents = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchMCPComponents"])();
                console.log("DEBUG: Fetched MCP components:", mcpComponents);
                // Log detailed information about MCP components
                console.log("DEBUG: MCP components structure:", mcpComponents);
                if (mcpComponents.MCP) {
                    console.log("DEBUG: MCP category exists with components:", Object.keys(mcpComponents.MCP));
                    console.log("DEBUG: First MCP component:", Object.values(mcpComponents.MCP)[0]);
                } else {
                    console.log("DEBUG: MCP category does not exist in mcpComponents");
                }
                // Merge the components
                const mergedComponents = {
                    ...fetchedComponents,
                    ...mcpComponents
                };
                // If both have MCP category, merge them
                if (fetchedComponents.MCP && mcpComponents.MCP) {
                    console.log("DEBUG: Both fetchedComponents and mcpComponents have MCP category, merging them");
                    mergedComponents.MCP = {
                        ...fetchedComponents.MCP,
                        ...mcpComponents.MCP
                    };
                }
                console.log("DEBUG: Merged components categories:", Object.keys(mergedComponents));
                console.log("DEBUG: Final MCP components count:", mergedComponents.MCP ? Object.keys(mergedComponents.MCP).length : 0);
                setComponentsData(mergedComponents);
            } catch (err) {
                const errorMsg = err instanceof Error ? err.message : "Unknown error";
                setComponentError(`Failed to load components: ${errorMsg}. Ensure the backend is running.`); // <<< Use renamed state
                console.error(err);
            } finally{
                setIsLoadingComponents(false); // <<< Use renamed state
            }
        };
        console.log("Loading components...");
        loadComponents();
    }, []); // Empty dependency array ensures this runs once on mount
    // Callback when nodes/edges change in the canvas
    const handleFlowChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((nodes, edges)=>{
        setCurrentNodes(nodes);
        setCurrentEdges(edges);
        // Mark as having unsaved changes when the flow is updated
        setHasUnsavedChanges(true);
    // Optional: console.log('Flow updated in parent:', nodes, edges);
    }, []); // Empty dependency array means this callback is stable
    // Callback for the Save to Server button
    const handleSaveWorkflowToServer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            // Get the current workflow title from state
            const filename = `${workflowTitle.replace(/\s+/g, "_")}`;
            // Call the API function to save to server
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveWorkflowToServer"])({
                nodes: currentNodes,
                edges: currentEdges,
                filename: filename,
                workflow_name: workflowTitle,
                ...workflowId && {
                    workflow_id: workflowId
                }
            });
            if (result.success) {
                // If this was a new workflow and we got an ID back, update our state
                if (!workflowId && result.workflow_id) {
                    console.log("Received workflow ID from server:", result.workflow_id);
                    setWorkflowId(result.workflow_id);
                    // Update URL without full page reload
                    window.history.replaceState({}, "", `?workflow_id=${result.workflow_id}`);
                }
                // Reset unsaved changes flag
                setHasUnsavedChanges(false);
                // Use the message field if available, otherwise use the default success message
                alert(result.message || `Workflow saved successfully on server at: ${result.filepath}`);
            } else {
                alert(`Failed to save workflow on server: ${result.error || "Unknown error"}`);
            }
        } catch (err) {
            const errorMsg = err instanceof Error ? err.message : String(err);
            console.error("Error in handleSaveWorkflowToServer:", err);
            alert(`Error saving workflow to server: ${errorMsg}`);
        }
    }, [
        currentNodes,
        currentEdges,
        workflowTitle,
        workflowId
    ]); // Depends on the current flow state
    // Callback for the Save button - validates and saves the workflow
    const handleSaveWorkflow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            // First validate the workflow
            const { validateBeforeSave } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$validationStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useValidationStore"].getState();
            const validationResult = await validateBeforeSave(currentNodes, currentEdges);
            if (!validationResult.isValid) {
                // Show validation errors
                const errorMessages = validationResult.errors.map((err)=>err.message).join("\n");
                alert(`Validation failed. Please fix the following issues:\n${errorMessages}`);
                return;
            }
            // Proceed with saving to server
            const filename = `${workflowTitle.replace(/\s+/g, "_")}`;
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveWorkflowToServer"])({
                nodes: currentNodes,
                edges: currentEdges,
                filename: filename,
                workflow_name: workflowTitle,
                ...workflowId && {
                    workflow_id: workflowId
                }
            });
            if (result.success) {
                // If this was a new workflow and we got an ID back, update our state
                if (!workflowId && result.workflow_id) {
                    console.log("Received workflow ID from server:", result.workflow_id);
                    setWorkflowId(result.workflow_id);
                    // Update URL without full page reload
                    window.history.replaceState({}, "", `?workflow_id=${result.workflow_id}`);
                }
                // Reset unsaved changes flag
                setHasUnsavedChanges(false);
                // Show success message
                alert(result.message || "Workflow saved successfully!");
            } else {
                // Show error message
                alert(`Failed to save workflow: ${result.error || "Unknown error"}`);
            }
        } catch (err) {
            const errorMsg = err instanceof Error ? err.message : String(err);
            console.error("Error in handleSaveWorkflow:", err);
            alert(`Error saving workflow: ${errorMsg}`);
        }
    }, [
        currentNodes,
        currentEdges,
        workflowTitle,
        workflowId
    ]); // Depends on the current flow state
    // --- Callback for the Run button ---
    const handleRunWorkflow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (isExecuting) return; // Prevent multiple simultaneous runs
        setIsExecuting(true);
        setExecutionResult(null); // Clear previous results
        try {
            // First validate the workflow
            const { validateBeforeExecution } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$validationStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useValidationStore"].getState();
            const validationResult = await validateBeforeExecution(currentNodes, currentEdges);
            if (!validationResult.isValid) {
                // Show validation errors
                const errorMessages = validationResult.errors.map((err)=>err.message).join("\n");
                alert(`Validation failed. Please fix the following issues:\n${errorMessages}`);
                setIsExecuting(false);
                return;
            }
            // Save the workflow first
            const filename = `${workflowTitle.replace(/\s+/g, "_")}`;
            const saveResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveWorkflowToServer"])({
                nodes: currentNodes,
                edges: currentEdges,
                filename: filename,
                workflow_name: workflowTitle,
                ...workflowId && {
                    workflow_id: workflowId
                }
            });
            if (!saveResult.success) {
                alert(`Failed to save workflow before execution: ${saveResult.error || "Unknown error"}`);
                setIsExecuting(false);
                return;
            }
            // Update workflow ID if this was a new workflow
            let currentWorkflowId = workflowId;
            if (!currentWorkflowId && saveResult.workflow_id) {
                currentWorkflowId = saveResult.workflow_id;
                setWorkflowId(currentWorkflowId);
                // Update URL without full page reload
                window.history.replaceState({}, "", `?workflow_id=${currentWorkflowId}`);
            }
            // Reset unsaved changes flag after successful save
            setHasUnsavedChanges(false);
            // Get the execution store
            const executionStore = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$executionStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useExecutionStore"].getState();
            // Set the missing fields from the validation result
            if (validationResult.missingFields) {
                executionStore.setMissingFields(validationResult.missingFields);
            }
            // Open the execution dialog
            executionStore.setDialogOpen(true);
            executionStore.setActiveTab("parameters");
            // Add a log entry
            executionStore.addLog("Workflow validated and saved successfully. Ready for execution.");
            // Set executing state to false since we're just opening the dialog
            setIsExecuting(false);
        // The actual execution will be handled by the ExecutionDialog component
        // when the user clicks the "Run" button in the dialog
        } catch (err) {
            // Catch unexpected errors
            const errorMsg = err instanceof Error ? err.message : String(err);
            console.error("Error in handleRunWorkflow:", err);
            setExecutionResult({
                success: false,
                error: `Frontend error: ${errorMsg}`
            });
            alert(`Error preparing workflow for execution: ${errorMsg}`);
            setIsExecuting(false);
        }
    }, [
        currentNodes,
        currentEdges,
        isExecuting,
        workflowId,
        workflowTitle,
        setHasUnsavedChanges
    ]); // Dependencies
    // Theme toggle handler
    const handleToggleTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setIsDarkMode((prev)=>!prev);
        // Apply dark mode class to the document
        document.documentElement.classList.toggle("dark");
    }, []);
    // Function to handle actions that might discard unsaved changes
    const handleActionWithUnsavedChanges = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((action)=>{
        if (hasUnsavedChanges) {
            // If there are unsaved changes, show the confirmation dialog
            setPendingAction(()=>action);
            setIsUnsavedChangesDialogOpen(true);
        } else {
            // If no unsaved changes, proceed with the action
            action();
        }
    }, [
        hasUnsavedChanges
    ]);
    // Handler for opening the load workflow modal
    const handleOpenLoadModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        handleActionWithUnsavedChanges(()=>{
            setIsLoadModalOpen(true);
        });
    }, [
        handleActionWithUnsavedChanges
    ]);
    // Handler for loading a workflow
    const handleLoadWorkflow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((workflowData, showSuccessMessage = true)=>{
        try {
            console.log("Loading workflow data:", workflowData);
            // Validate and convert the workflow data
            const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$workflowUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["loadWorkflowFromJson"])(workflowData);
            console.log("Validation result:", result);
            if (!result.isValid || !result.data) {
                // Use a more modern notification approach instead of alert
                console.error(`Failed to load workflow: ${result.error || "Invalid workflow data"}`);
                return;
            }
            // We don't need this check anymore as we're using the UnsavedChangesDialog
            // The dialog is shown before opening the load modal if there are unsaved changes
            // Check if the workflow data has a StartNode and ensure its configuration is preserved
            const nodes = result.data.nodes;
            const startNode = nodes.find((node)=>node.data.originalType === "StartNode");
            if (startNode) {
                console.log("StartNode found in loaded workflow:", startNode);
                // Ensure the StartNode has a config object with collected_parameters
                if (!startNode.data.config) {
                    startNode.data.config = {};
                }
                if (!startNode.data.config.collected_parameters) {
                    startNode.data.config.collected_parameters = {};
                }
                // Ensure all parameters in collected_parameters have the required property set
                // This is critical for pre-built workflows where the required property might not be set
                if (startNode.data.config.collected_parameters) {
                    Object.keys(startNode.data.config.collected_parameters).forEach((paramId)=>{
                        const param = startNode.data.config.collected_parameters[paramId];
                        // If required is undefined, set it to true (consider required unless explicitly false)
                        if (param.required === undefined) {
                            console.log(`Setting required=true for parameter ${paramId} in StartNode`);
                            param.required = true;
                        }
                    });
                }
                console.log("StartNode config after ensuring structure:", startNode.data.config);
                // Store the StartNode's collected parameters in the window object
                // This will make them available to the ExecutionDialog component
                window.startNodeCollectedParameters = startNode.data.config?.collected_parameters || {};
                console.log("Stored StartNode collected parameters in window object:", window.startNodeCollectedParameters);
            } else {
                console.log("No StartNode found in loaded workflow, this may cause issues");
                // Clear any previously stored parameters
                window.startNodeCollectedParameters = {};
            }
            // Update the workflow state
            console.log("Setting workflow state with:", result.data);
            setLoadedWorkflow(result.data);
            setCurrentNodes(result.data.nodes);
            setCurrentEdges(result.data.edges);
            // Update the workflow title if available
            if (workflowData.workflow_name) {
                console.log("Setting workflow title from workflowData.workflow_name:", workflowData.workflow_name);
                setWorkflowTitle(workflowData.workflow_name);
            } else {
                console.log("No workflow_name found in workflowData, keeping current title:", workflowTitle);
            }
            // Reset unsaved changes flag after successful load
            setHasUnsavedChanges(false);
            // Only show success message if explicitly requested
            // This prevents showing alerts when loading from URL parameters
            if (showSuccessMessage) {
                // Use a temporary notification in the UI instead of an alert
                setExecutionResult({
                    success: true,
                    message: "Workflow loaded successfully!"
                });
                // Auto-hide the notification after 3 seconds
                setTimeout(()=>{
                    setExecutionResult(null);
                }, 3000);
            }
        } catch (error) {
            console.error("Error loading workflow:", error);
            // Use a more modern notification approach instead of alert
            setExecutionResult({
                success: false,
                error: `Error loading workflow: ${error instanceof Error ? error.message : "Unknown error"}`
            });
            // Auto-hide the error notification after 5 seconds
            setTimeout(()=>{
                setExecutionResult(null);
            }, 5000);
        }
    }, [
        workflowTitle
    ]); // Include workflowTitle as a dependency
    // Apply initial theme based on system preference
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
        setIsDarkMode(prefersDark);
        if (prefersDark) {
            document.documentElement.classList.add("dark");
        }
    }, []);
    // Effect to add beforeunload event listener for unsaved changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleBeforeUnload = (e)=>{
            if (hasUnsavedChanges) {
                // Standard way to show a confirmation dialog when leaving the page
                const message = "You have unsaved changes. Are you sure you want to leave?";
                e.preventDefault();
                // Modern browsers require both preventDefault and setting returnValue
                // Even though returnValue is deprecated, it's still required for this to work
                // @ts-expect-error - Ignore the deprecation warning
                e.returnValue = message;
                return message;
            }
        };
        window.addEventListener("beforeunload", handleBeforeUnload);
        return ()=>{
            window.removeEventListener("beforeunload", handleBeforeUnload);
        };
    }, [
        hasUnsavedChanges
    ]);
    // Memoized callback for toggling sidebar
    const handleToggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setIsSidebarCollapsed((prev)=>!prev);
    }, []);
    // Effect to add keyboard shortcut for toggling sidebar
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleKeyDown = (e)=>{
            // Toggle sidebar with Alt+S
            if (e.altKey && e.key === "s") {
                handleToggleSidebar();
            }
        };
        window.addEventListener("keydown", handleKeyDown);
        return ()=>{
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, [
        handleToggleSidebar
    ]);
    // We'll use the WorkflowLoader component instead of this effect
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
        className: `relative flex h-screen w-screen flex-col overflow-hidden ${isDarkMode ? "dark" : ""}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Suspense"], {
                fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: "Loading workflow..."
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 640,
                    columnNumber: 27
                }, void 0),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(WorkflowLoader, {
                    onWorkflowLoad: handleLoadWorkflow,
                    onWorkflowIdChange: setWorkflowId,
                    onLoadingChange: setIsLoadingWorkflow,
                    onErrorChange: setWorkflowLoadError,
                    workflowId: workflowId,
                    loadedWorkflow: loadedWorkflow
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 641,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 640,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$TopBar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TopBar"], {
                onSave: handleSaveWorkflow,
                onRun: handleRunWorkflow,
                workflowTitle: workflowTitle,
                onTitleChange: setWorkflowTitle,
                onValidate: ()=>console.log("Validate workflow"),
                isDarkMode: isDarkMode,
                onToggleTheme: handleToggleTheme,
                onLoad: handleOpenLoadModal,
                className: "flex-shrink-0",
                nodes: currentNodes,
                edges: currentEdges
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 651,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-grow overflow-hidden",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Sidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Sidebar"], {
                        components: components,
                        collapsed: isSidebarCollapsed,
                        onToggleCollapse: handleToggleSidebar
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 666,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-background/50 flex flex-grow flex-col overflow-hidden backdrop-blur-sm",
                        children: [
                            isLoadingComponents && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-grow items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex animate-pulse flex-col items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-brand-primary/20 mb-4 h-12 w-12 rounded-full"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 675,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-brand-primary/20 h-4 w-48 rounded"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 676,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-secondary text-brand-secondary-font mt-2 text-sm",
                                            children: "Loading components..."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 677,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 674,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 673,
                                columnNumber: 13
                            }, this),
                            componentError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-grow items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "border-brand-unpublish/30 bg-brand-unpublish/10 max-w-md rounded-lg border p-6 text-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-primary text-brand-unpublish mb-2 font-medium",
                                            children: "Error Loading Components"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 686,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-secondary text-brand-secondary-font text-sm",
                                            children: componentError
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 689,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            className: "border-brand-unpublish/30 text-brand-unpublish hover:bg-brand-unpublish/10 mt-4 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium",
                                            onClick: ()=>window.location.reload(),
                                            children: "Retry"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 692,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 685,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 684,
                                columnNumber: 13
                            }, this),
                            isLoadingWorkflow && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-grow items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "border-brand-stroke bg-brand-card-hover max-w-md rounded-lg border p-6 text-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                            className: "text-brand-primary mx-auto mb-4 h-8 w-8 animate-spin"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 705,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-primary text-brand-primary-font dark:text-brand-white-text mb-2 font-medium",
                                            children: "Loading Workflow"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 706,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-secondary text-brand-secondary-font text-sm",
                                            children: "Please wait while we load your workflow..."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 709,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 704,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 703,
                                columnNumber: 13
                            }, this),
                            workflowLoadError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-grow items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "border-brand-unpublish/30 bg-brand-unpublish/10 max-w-md rounded-lg border p-6 text-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-primary text-brand-unpublish mb-2 font-medium",
                                            children: "Error Loading Workflow"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 719,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-secondary text-brand-secondary-font mb-4 text-sm",
                                            children: workflowLoadError
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 722,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    className: "border-brand-unpublish/30 text-brand-unpublish hover:bg-brand-unpublish/10 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium",
                                                    onClick: ()=>window.location.reload(),
                                                    children: "Try Again"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 726,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/home",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        type: "button",
                                                        className: "brand-gradient-indicator text-brand-white-text inline-flex h-8 items-center justify-center rounded-md px-3 py-2 text-sm font-medium",
                                                        children: "Back to Home"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 734,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 733,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 725,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 718,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 717,
                                columnNumber: 13
                            }, this),
                            !isLoadingComponents && !componentError && !isLoadingWorkflow && !workflowLoadError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$canvas$2f$WorkflowCanvas$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                onFlowChange: handleFlowChange,
                                initialWorkflow: loadedWorkflow || undefined
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 747,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 671,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 665,
                columnNumber: 7
            }, this),
            isExecuting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "brand-gradient-indicator text-brand-white-text animate-in fade-in slide-in-from-bottom-4 absolute bottom-4 left-4 z-50 rounded-lg p-3 text-sm shadow-lg",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "h-2 w-2 animate-ping rounded-full bg-white/80"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 759,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-primary",
                            children: "Executing workflow..."
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 760,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 758,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 757,
                columnNumber: 9
            }, this),
            executionResult && !isExecuting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `animate-in fade-in slide-in-from-bottom-4 absolute right-4 bottom-4 z-50 rounded-lg p-3 text-sm shadow-lg ${executionResult.success ? "bg-brand-tick text-white" : "bg-brand-unpublish text-white"}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-2",
                    children: executionResult.success ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-primary",
                        children: "Workflow executed successfully!"
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 772,
                        columnNumber: 15
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-primary",
                        children: [
                            "Error: ",
                            executionResult.error
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 774,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 770,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 767,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$modals$2f$LoadWorkflowModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LoadWorkflowModal"], {
                isOpen: isLoadModalOpen,
                onClose: ()=>setIsLoadModalOpen(false),
                onLoadWorkflow: handleLoadWorkflow
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 781,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$modals$2f$UnsavedChangesDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UnsavedChangesDialog"], {
                isOpen: isUnsavedChangesDialogOpen,
                onClose: ()=>setIsUnsavedChangesDialogOpen(false),
                onContinue: ()=>{
                    setIsUnsavedChangesDialogOpen(false);
                    if (pendingAction) {
                        pendingAction();
                        setPendingAction(null);
                    }
                }
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 788,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 636,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_c67dd54e._.js.map