{"nodes": [{"id": "MergeDataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "MergeDataComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "object", "description": "The main data structure to merge. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional inputs to show (1-10)."}, "required": false}, {"field_name": "merge_strategy", "data_type": {"type": "string", "description": "How to handle conflicts when merging dictionaries."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "object", "description": "Data structure 1 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "object", "description": "Data structure 2 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "object", "description": "Data structure 3 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "object", "description": "Data structure 4 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "object", "description": "Data structure 5 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "object", "description": "Data structure 6 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "object", "description": "Data structure 7 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "object", "description": "Data structure 8 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "object", "description": "Data structure 9 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "object", "description": "Data structure 10 to merge. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_data", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "ConditionalNode", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "ConditionalNode", "input_schema": {"predefined_fields": [{"field_name": "primary_input_data", "data_type": {"type": "string", "description": "Main data to route through conditions. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "condition_1_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_1_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_1_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_1_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_1_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_1_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_2_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_2_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_2_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_2_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_2_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_2_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "num_additional_conditions", "data_type": {"type": "number", "description": "Number of additional conditions beyond the base 2 conditions (0-8). Total conditions will be 2 + this value."}, "required": false}, {"field_name": "condition_3_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_3_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_3_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_3_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_3_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_3_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_4_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_4_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_4_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_4_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_4_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_4_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_5_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_5_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_5_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_5_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_5_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_5_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_6_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_6_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_6_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_6_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_6_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_6_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_7_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_7_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_7_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_7_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_7_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_7_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_8_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_8_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_8_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_8_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_8_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_8_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_9_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_9_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_9_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_9_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_9_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_9_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_10_source", "data_type": {"type": "string", "description": "Source of data for condition evaluation"}, "required": true}, {"field_name": "condition_10_variable", "data_type": {"type": "string", "description": "Global context variable name (only for global_context source)"}, "required": false}, {"field_name": "condition_10_operator", "data_type": {"type": "string", "description": "Comparison operator to apply"}, "required": true}, {"field_name": "condition_10_expected_value", "data_type": {"type": "string", "description": "Value to compare against (not used for exists/is_empty operators)"}, "required": false}, {"field_name": "condition_10_use_primary", "data_type": {"type": "boolean", "description": "Route primary input data when this condition matches"}, "required": false}, {"field_name": "condition_10_custom_input", "data_type": {"type": "string", "description": "Custom data to route when condition matches (only if not using primary input)"}, "required": false}, {"field_name": "condition_1_input_handle", "data_type": {"type": "string", "description": "Input data for condition 1 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_2_input_handle", "data_type": {"type": "string", "description": "Input data for condition 2 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_3_input_handle", "data_type": {"type": "string", "description": "Input data for condition 3 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_4_input_handle", "data_type": {"type": "string", "description": "Input data for condition 4 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_5_input_handle", "data_type": {"type": "string", "description": "Input data for condition 5 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_6_input_handle", "data_type": {"type": "string", "description": "Input data for condition 6 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_7_input_handle", "data_type": {"type": "string", "description": "Input data for condition 7 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_8_input_handle", "data_type": {"type": "string", "description": "Input data for condition 8 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_9_input_handle", "data_type": {"type": "string", "description": "Input data for condition 9 evaluation (only when source is Node Output)"}, "required": false}, {"field_name": "condition_10_input_handle", "data_type": {"type": "string", "description": "Input data for condition 10 evaluation (only when source is Node Output)"}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "default_output", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "AlterMetadataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AlterMetadataComponent", "input_schema": {"predefined_fields": [{"field_name": "input_metadata", "data_type": {"type": "object", "description": "The metadata dictionary to modify. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "updates", "data_type": {"type": "object", "description": "Dictionary of key-value pairs to update or add to the metadata. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "keys_to_remove", "data_type": {"type": "array", "description": "List of keys to remove from the metadata. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_metadata", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "CombineTextComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "CombineTextComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "string", "description": "The main text or list to combine. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional text inputs to show (1-10)."}, "required": false}, {"field_name": "separator", "data_type": {"type": "string", "description": "The character or string to join the text with."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "string", "description": "Text for input 1. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "string", "description": "Text for input 2. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "string", "description": "Text for input 3. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "string", "description": "Text for input 4. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "string", "description": "Text for input 5. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "string", "description": "Text for input 6. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "string", "description": "Text for input 7. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "string", "description": "Text for input 8. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "string", "description": "Text for input 9. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "string", "description": "Text for input 10. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "result", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "DataToDataFrameComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "DataToDataFrameComponent", "input_schema": {"predefined_fields": [{"field_name": "input_data", "data_type": {"type": "object", "description": "The data to convert to a DataFrame. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "orientation", "data_type": {"type": "string", "description": "The orientation of the input data: 'records' (list of dicts), 'columns' (dict of lists), or 'auto-detect'."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_dataframe", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "MessageToDataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "MessageToDataComponent", "input_schema": {"predefined_fields": [{"field_name": "input_message", "data_type": {"type": "object", "description": "The Message object to extract fields from. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "fields_to_extract", "data_type": {"type": "array", "description": "List of field names to extract from the message. Leave empty to extract all fields. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_data", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}], "transitions": [{"id": "transition-MergeDataComponent-1748500225463", "sequence": 1, "transition_type": "initial", "execution_type": "Components", "node_info": {"node_id": "MergeDataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "MergeDataComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "object", "field_value": null}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": null}, {"field_name": "merge_strategy", "data_type": "string", "field_value": null}, {"field_name": "input_1", "data_type": "object", "field_value": null}, {"field_name": "input_2", "data_type": "object", "field_value": null}, {"field_name": "input_3", "data_type": "object", "field_value": null}, {"field_name": "input_4", "data_type": "object", "field_value": null}, {"field_name": "input_5", "data_type": "object", "field_value": null}, {"field_name": "input_6", "data_type": "object", "field_value": null}, {"field_name": "input_7", "data_type": "object", "field_value": null}, {"field_name": "input_8", "data_type": "object", "field_value": null}, {"field_name": "input_9", "data_type": "object", "field_value": null}, {"field_name": "input_10", "data_type": "object", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-start-node", "source_node_id": "Start", "data_type": "string", "mapping": [{"from_field": "flow", "to_field": "main_input", "source_handle": "flow", "target_handle": "main_input", "edge_id": "reactflow__edge-start-nodeflow-MergeDataComponent-1748500225463main_input", "mapping_type": "direct", "confidence": "low"}]}], "output_data": [{"to_transition_id": "transition-ConditionalNode-1748500238663", "target_node_id": "Switch-Case Router", "data_type": "string", "output_metadata": {"source_handle": "output_data", "target_handle": "primary_input_data", "edge_id": "reactflow__edge-MergeDataComponent-1748500225463output_data-ConditionalNode-1748500238663primary_input_data", "expected_output_fields": ["output_data", "error"], "field_mappings": {"output_data": "output_data"}}}]}, "approval_required": false, "end": false}, {"id": "transition-ConditionalNode-1748500238663", "sequence": 2, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "ConditionalNode", "tools_to_use": [{"tool_id": 1, "tool_name": "ConditionalNode", "tool_params": {"items": [{"field_name": "primary_input_data", "data_type": "string", "field_value": "${primary_input_data}"}, {"field_name": "condition_1_source", "data_type": "string", "field_value": "node_output"}, {"field_name": "condition_1_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_1_operator", "data_type": "string", "field_value": "ends_with"}, {"field_name": "condition_1_expected_value", "data_type": "string", "field_value": "hello"}, {"field_name": "condition_1_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_1_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_2_source", "data_type": "string", "field_value": "global_context"}, {"field_name": "condition_2_variable", "data_type": "string", "field_value": "markeiting"}, {"field_name": "condition_2_operator", "data_type": "string", "field_value": "exists"}, {"field_name": "condition_2_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_2_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_2_custom_input", "data_type": "string", "field_value": null}, {"field_name": "num_additional_conditions", "data_type": "number", "field_value": "1"}, {"field_name": "condition_3_source", "data_type": "string", "field_value": "node_output"}, {"field_name": "condition_3_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_3_operator", "data_type": "string", "field_value": "contains"}, {"field_name": "condition_3_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_3_use_primary", "data_type": "boolean", "field_value": true}, {"field_name": "condition_3_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_4_source", "data_type": "string", "field_value": null}, {"field_name": "condition_4_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_4_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_4_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_4_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_4_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_5_source", "data_type": "string", "field_value": null}, {"field_name": "condition_5_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_5_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_5_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_5_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_5_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_6_source", "data_type": "string", "field_value": null}, {"field_name": "condition_6_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_6_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_6_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_6_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_6_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_7_source", "data_type": "string", "field_value": null}, {"field_name": "condition_7_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_7_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_7_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_7_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_7_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_8_source", "data_type": "string", "field_value": null}, {"field_name": "condition_8_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_8_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_8_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_8_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_8_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_9_source", "data_type": "string", "field_value": null}, {"field_name": "condition_9_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_9_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_9_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_9_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_9_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_10_source", "data_type": "string", "field_value": null}, {"field_name": "condition_10_variable", "data_type": "string", "field_value": null}, {"field_name": "condition_10_operator", "data_type": "string", "field_value": null}, {"field_name": "condition_10_expected_value", "data_type": "string", "field_value": null}, {"field_name": "condition_10_use_primary", "data_type": "boolean", "field_value": null}, {"field_name": "condition_10_custom_input", "data_type": "string", "field_value": null}, {"field_name": "condition_1_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_2_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_3_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_4_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_5_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_6_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_7_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_8_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_9_input_handle", "data_type": "string", "field_value": null}, {"field_name": "condition_10_input_handle", "data_type": "string", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-MergeDataComponent-1748500225463", "source_node_id": "Merge Data", "data_type": "string", "mapping": [{"from_field": "result.result", "to_field": "primary_input_data", "source_handle": "output_data", "target_handle": "primary_input_data", "edge_id": "reactflow__edge-MergeDataComponent-1748500225463output_data-ConditionalNode-1748500238663primary_input_data", "mapping_type": "nested", "confidence": "medium"}]}], "output_data": [{"to_transition_id": "transition-AlterMetadataComponent-1748500346326", "target_node_id": "<PERSON>er Metada<PERSON>", "data_type": "string", "output_metadata": {"source_handle": "condition_1_output", "target_handle": "input_metadata", "edge_id": "reactflow__edge-ConditionalNode-1748500238663condition_1_output-AlterMetadataComponent-1748500346326input_metadata", "expected_output_fields": ["default_output"], "field_mappings": {"condition_1_output": "condition_1_output"}}}, {"to_transition_id": "transition-CombineTextComponent-1748500353589", "target_node_id": "Combine Text", "data_type": "string", "output_metadata": {"source_handle": "condition_2_output", "target_handle": "main_input", "edge_id": "reactflow__edge-ConditionalNode-1748500238663condition_2_output-CombineTextComponent-1748500353589main_input", "expected_output_fields": ["default_output"], "field_mappings": {"condition_2_output": "condition_2_output"}}}, {"to_transition_id": "transition-DataToDataFrameComponent-1748500360042", "target_node_id": "Data to DataFrame", "data_type": "string", "output_metadata": {"source_handle": "condition_3_output", "target_handle": "input_data", "edge_id": "reactflow__edge-ConditionalNode-1748500238663condition_3_output-DataToDataFrameComponent-1748500360042input_data", "expected_output_fields": ["default_output"], "field_mappings": {"condition_3_output": "condition_3_output"}}}, {"to_transition_id": "transition-MessageToDataComponent-1748500371222", "target_node_id": "Message To Data", "data_type": "string", "output_metadata": {"source_handle": "default_output", "target_handle": "input_message", "edge_id": "reactflow__edge-ConditionalNode-1748500238663default_output-MessageToDataComponent-1748500371222input_message", "expected_output_fields": ["default_output"], "field_mappings": {"default_output": "default_output"}}}]}, "approval_required": false, "end": false}, {"id": "transition-AlterMetadataComponent-1748500346326", "sequence": 3, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "AlterMetadataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "AlterMetadataComponent", "tool_params": {"items": [{"field_name": "input_metadata", "data_type": "object", "field_value": "${input_metadata}"}, {"field_name": "updates", "data_type": "object", "field_value": null}, {"field_name": "keys_to_remove", "data_type": "array", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-ConditionalNode-1748500238663", "source_node_id": "Switch-Case Router", "data_type": "string", "mapping": [{"from_field": "condition_1_output", "to_field": "input_metadata", "source_handle": "condition_1_output", "target_handle": "input_metadata", "edge_id": "reactflow__edge-ConditionalNode-1748500238663condition_1_output-AlterMetadataComponent-1748500346326input_metadata", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}, {"id": "transition-MessageToDataComponent-1748500371222", "sequence": 4, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "MessageToDataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "MessageToDataComponent", "tool_params": {"items": [{"field_name": "input_message", "data_type": "object", "field_value": "${input_message}"}, {"field_name": "fields_to_extract", "data_type": "array", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-ConditionalNode-1748500238663", "source_node_id": "Switch-Case Router", "data_type": "string", "mapping": [{"from_field": "default_output", "to_field": "input_message", "source_handle": "default_output", "target_handle": "input_message", "edge_id": "reactflow__edge-ConditionalNode-1748500238663default_output-MessageToDataComponent-1748500371222input_message", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}, {"id": "transition-CombineTextComponent-1748500353589", "sequence": 5, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "CombineTextComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "CombineTextComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "string", "field_value": "${main_input}"}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": null}, {"field_name": "separator", "data_type": "string", "field_value": null}, {"field_name": "input_1", "data_type": "string", "field_value": null}, {"field_name": "input_2", "data_type": "string", "field_value": null}, {"field_name": "input_3", "data_type": "string", "field_value": null}, {"field_name": "input_4", "data_type": "string", "field_value": null}, {"field_name": "input_5", "data_type": "string", "field_value": null}, {"field_name": "input_6", "data_type": "string", "field_value": null}, {"field_name": "input_7", "data_type": "string", "field_value": null}, {"field_name": "input_8", "data_type": "string", "field_value": null}, {"field_name": "input_9", "data_type": "string", "field_value": null}, {"field_name": "input_10", "data_type": "string", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-ConditionalNode-1748500238663", "source_node_id": "Switch-Case Router", "data_type": "string", "mapping": [{"from_field": "condition_2_output", "to_field": "main_input", "source_handle": "condition_2_output", "target_handle": "main_input", "edge_id": "reactflow__edge-ConditionalNode-1748500238663condition_2_output-CombineTextComponent-1748500353589main_input", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}, {"id": "transition-DataToDataFrameComponent-1748500360042", "sequence": 6, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "DataToDataFrameComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "DataToDataFrameComponent", "tool_params": {"items": [{"field_name": "input_data", "data_type": "object", "field_value": "${input_data}"}, {"field_name": "orientation", "data_type": "string", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-ConditionalNode-1748500238663", "source_node_id": "Switch-Case Router", "data_type": "string", "mapping": [{"from_field": "condition_3_output", "to_field": "input_data", "source_handle": "condition_3_output", "target_handle": "input_data", "edge_id": "reactflow__edge-ConditionalNode-1748500238663condition_3_output-DataToDataFrameComponent-1748500360042input_data", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}]}